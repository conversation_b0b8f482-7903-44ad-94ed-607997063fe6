/**
 * 存储模块测试用例
 * 测试存储模块的基本功能、数据过期和空间监控
 */

// 测试配置
const TEST_CONFIG = {
  // 测试键值
  testKey: 'test_storage_key',
  testValue: { name: 'Test Data', value: 42, timestamp: Date.now() },
  
  // 测试过期时间（毫秒）
  shortExpiration: 100, // 100毫秒，用于快速测试过期
  longExpiration: 1000 * 60 * 60, // 1小时
  
  // 测试延迟
  delay: 200 // 200毫秒
};

/**
 * 延迟执行函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise<void>}
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试基本存储功能
 */
async function testBasicStorage() {
  console.log('开始测试基本存储功能...');
  
  try {
    const Storage = window.AIPDD.Storage;
    if (!Storage) {
      throw new Error('存储模块未加载');
    }
    
    // 测试存储数据
    await Storage.set(TEST_CONFIG.testKey, TEST_CONFIG.testValue);
    console.log('✓ 数据存储成功');
    
    // 测试读取数据
    const retrievedValue = await Storage.get(TEST_CONFIG.testKey);
    console.log('读取的数据:', retrievedValue);
    
    // 验证数据一致性
    const isEqual = JSON.stringify(retrievedValue) === JSON.stringify(TEST_CONFIG.testValue);
    if (isEqual) {
      console.log('✓ 数据读取成功，且与存储的数据一致');
    } else {
      throw new Error('数据不一致');
    }
    
    // 测试删除数据
    await Storage.remove(TEST_CONFIG.testKey);
    const afterRemoval = await Storage.get(TEST_CONFIG.testKey);
    
    if (afterRemoval === null || afterRemoval === undefined) {
      console.log('✓ 数据删除成功');
    } else {
      throw new Error('数据删除失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 基本存储测试失败:', error);
    return false;
  }
}

/**
 * 测试数据过期机制
 */
async function testDataExpiration() {
  console.log('开始测试数据过期机制...');
  
  try {
    const Storage = window.AIPDD.Storage;
    if (!Storage) {
      throw new Error('存储模块未加载');
    }
    
    // 测试短期过期
    const shortExpiryKey = `${TEST_CONFIG.testKey}_short_expiry`;
    await Storage.set(shortExpiryKey, TEST_CONFIG.testValue, { expiration: TEST_CONFIG.shortExpiration });
    console.log(`✓ 数据已存储，过期时间设为 ${TEST_CONFIG.shortExpiration}ms`);
    
    // 立即读取，应该能获取到数据
    const immediateValue = await Storage.get(shortExpiryKey);
    if (immediateValue) {
      console.log('✓ 过期前能正确读取数据');
    } else {
      throw new Error('过期前无法读取数据');
    }
    
    // 等待过期
    console.log(`等待 ${TEST_CONFIG.delay}ms 让数据过期...`);
    await delay(TEST_CONFIG.delay);
    
    // 过期后读取，应该返回null或默认值
    const afterExpiryValue = await Storage.get(shortExpiryKey, 'default_value');
    if (afterExpiryValue === 'default_value') {
      console.log('✓ 数据已正确过期，返回默认值');
    } else {
      throw new Error('数据未正确过期');
    }
    
    // 测试长期过期（不等待实际过期，只验证设置）
    const longExpiryKey = `${TEST_CONFIG.testKey}_long_expiry`;
    await Storage.set(longExpiryKey, TEST_CONFIG.testValue, { expiration: TEST_CONFIG.longExpiration });
    
    // 检查是否成功设置
    const longExpiryValue = await Storage.get(longExpiryKey);
    if (longExpiryValue) {
      console.log('✓ 长期过期数据设置成功');
      // 清理测试数据
      await Storage.remove(longExpiryKey);
    } else {
      throw new Error('长期过期数据设置失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 数据过期测试失败:', error);
    return false;
  }
}

/**
 * 测试存储空间监控与清理
 */
async function testStorageMonitoring() {
  console.log('开始测试存储空间监控与清理...');
  
  try {
    const Storage = window.AIPDD.Storage;
    if (!Storage) {
      throw new Error('存储模块未加载');
    }
    
    // 检查存储使用情况
    const usage = await Storage.checkUsage();
    console.log('当前存储使用情况:', usage);
    
    if (usage && typeof usage.bytesInUse === 'number' && typeof usage.percentUsed === 'number') {
      console.log('✓ 存储使用情况检查成功');
    } else {
      throw new Error('存储使用情况检查失败');
    }
    
    // 测试存储清理
    const cleanupResult = await Storage.cleanup();
    console.log('存储清理结果:', cleanupResult);
    
    // 验证清理后的使用情况
    const usageAfterCleanup = await Storage.checkUsage();
    console.log('清理后存储使用情况:', usageAfterCleanup);
    
    // 注意：这里我们不能确定清理会减少使用量，因为可能没有可清理的内容
    // 所以只验证能否正常执行清理函数
    console.log('✓ 存储清理功能测试成功');
    
    return true;
  } catch (error) {
    console.error('❌ 存储监控测试失败:', error);
    return false;
  }
}

/**
 * 测试批量操作
 */
async function testBulkOperations() {
  console.log('开始测试批量操作...');
  
  try {
    const Storage = window.AIPDD.Storage;
    if (!Storage) {
      throw new Error('存储模块未加载');
    }
    
    // 测试批量存储
    const batchSize = 10;
    const keys = [];
    
    console.log(`存储 ${batchSize} 条测试数据...`);
    for (let i = 0; i < batchSize; i++) {
      const key = `${TEST_CONFIG.testKey}_batch_${i}`;
      keys.push(key);
      await Storage.set(key, { index: i, value: `Test value ${i}` });
    }
    
    // 获取所有键
    const allKeys = await Storage.getAllKeys();
    console.log('获取到的所有键:', allKeys);
    
    // 检查是否所有测试键都存在
    const allKeysExist = keys.every(key => allKeys.includes(key));
    if (allKeysExist) {
      console.log('✓ 批量存储测试成功');
    } else {
      throw new Error('批量存储测试失败，部分键不存在');
    }
    
    // 清理测试数据
    console.log('清理测试数据...');
    for (const key of keys) {
      await Storage.remove(key);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 批量操作测试失败:', error);
    return false;
  }
}

/**
 * 测试专用存储区域
 */
async function testSpecializedStorage() {
  console.log('开始测试专用存储区域...');
  
  try {
    const Storage = window.AIPDD.Storage;
    if (!Storage) {
      throw new Error('存储模块未加载');
    }
    
    // 测试设置存储
    if (Storage.settings) {
      await Storage.settings.set('test_setting', { enabled: true, value: 42 });
      const settingValue = await Storage.settings.get('test_setting');
      
      if (settingValue && settingValue.enabled === true) {
        console.log('✓ 设置存储测试成功');
      } else {
        throw new Error('设置存储测试失败');
      }
    } else {
      console.log('⚠️ 设置存储区域不可用，跳过测试');
    }
    
    // 测试消息存储
    if (Storage.messages) {
      const conversationId = 'test_conversation';
      const messageId = 'test_message';
      
      await Storage.messages.set(conversationId, messageId, { text: 'Hello', timestamp: Date.now() });
      const messageValue = await Storage.messages.get(conversationId, messageId);
      
      if (messageValue && messageValue.text === 'Hello') {
        console.log('✓ 消息存储测试成功');
      } else {
        throw new Error('消息存储测试失败');
      }
      
      // 测试已回复标记
      await Storage.messages.markReplied(conversationId, messageId);
      const isReplied = await Storage.messages.isReplied(conversationId, messageId);
      
      if (isReplied) {
        console.log('✓ 消息已回复标记测试成功');
      } else {
        throw new Error('消息已回复标记测试失败');
      }
    } else {
      console.log('⚠️ 消息存储区域不可用，跳过测试');
    }
    
    // 测试关键词存储
    if (Storage.keywords) {
      const keywordType = 'test';
      
      // 添加关键词
      await Storage.keywords.add(keywordType, '测试关键词1');
      await Storage.keywords.add(keywordType, '测试关键词2');
      
      // 获取关键词
      const keywords = await Storage.keywords.get(keywordType);
      
      if (keywords && keywords.length === 2 && keywords.includes('测试关键词1')) {
        console.log('✓ 关键词存储测试成功');
      } else {
        throw new Error('关键词存储测试失败');
      }
      
      // 删除关键词
      await Storage.keywords.remove(keywordType, '测试关键词1');
      const afterRemoval = await Storage.keywords.get(keywordType);
      
      if (afterRemoval && afterRemoval.length === 1 && !afterRemoval.includes('测试关键词1')) {
        console.log('✓ 关键词删除测试成功');
      } else {
        throw new Error('关键词删除测试失败');
      }
      
      // 清理测试数据
      await Storage.keywords.set(keywordType, []);
    } else {
      console.log('⚠️ 关键词存储区域不可用，跳过测试');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 专用存储区域测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('===== 存储模块测试开始 =====');
  
  let passedCount = 0;
  let failedCount = 0;
  
  // 测试基本存储功能
  if (await testBasicStorage()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试数据过期机制
  if (await testDataExpiration()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试存储空间监控与清理
  if (await testStorageMonitoring()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试批量操作
  if (await testBulkOperations()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试专用存储区域
  if (await testSpecializedStorage()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n===== 存储模块测试结束 =====');
  console.log(`通过: ${passedCount} 测试`);
  console.log(`失败: ${failedCount} 测试`);
  
  return {
    total: passedCount + failedCount,
    passed: passedCount,
    failed: failedCount,
    success: failedCount === 0
  };
}

// 导出测试函数
window.StorageModuleTest = {
  runAllTests,
  testBasicStorage,
  testDataExpiration,
  testStorageMonitoring,
  testBulkOperations,
  testSpecializedStorage
}; 