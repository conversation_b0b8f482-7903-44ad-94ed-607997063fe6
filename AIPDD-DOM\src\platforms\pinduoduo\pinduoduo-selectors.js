// 拼多多平台选择器配置
window.AIPDD = window.AIPDD || {};
window.AIPDD.Platforms = window.AIPDD.Platforms || {};
window.AIPDD.Platforms.Pinduoduo = window.AIPDD.Platforms.Pinduoduo || {};

(function() {
  // 获取Logger，如果已加载
  const Logger = window.AIPDD.Core?.Utils?.Logger || {
    debug: console.debug.bind(console)
  };
  
  // 平台特定的DOM元素选择器
  const SELECTORS = {
    // 聊天相关
    CHAT_INPUT: '.chat-message-input',                  // 聊天输入框
    SEND_BUTTON: '.send-btn',                          // 发送按钮
    CHAT_LIST: '.chat-list',                           // 聊天列表
    CHAT_ITEM: '.chat-item',                           // 聊天项
    CHAT_ITEM_BOX: '.chat-item-box',                   // 聊天项盒子
    UNREAD_COUNT: '.unread-count',                     // 未读消息数量
    CHAT_PORTRAIT: '.chat-portrait',                   // 聊天头像
    UNREAD_INDICATOR: '.chat-portrait > i',            // 未读指示器
    
    // 消息相关
    MESSAGE_CONTAINER: '.message-container',           // 消息容器
    MESSAGE_ITEM: '.message-item',                     // 消息项
    BUYER_MESSAGE: '.buyer-item',                      // 买家消息
    SELLER_MESSAGE: '.seller-item',                    // 卖家消息
    MESSAGE_CONTENT: '.msg-content-box, .text-content', // 消息内容
    
    // 商品卡片相关
    PRODUCT_CARD: '.msg-content.good-card',            // 商品卡片
    PRODUCT_NAME: '.good-name',                        // 商品名称
    PRODUCT_PRICE: '.good-price',                      // 商品价格
    PRODUCT_ID: '.good-id',                            // 商品ID
    PRODUCT_SPEC: '.msg-content.lego-card [class*="mallGoodsSkuCard"]', // 商品规格卡片
    
    // 特殊消息类型
    NOTIFY_CARD: '.notify-card',                       // 通知卡片
    ORDER_CARD: '.order-card',                         // 订单卡片
    REFUND_CARD: '.refund-card, .apply-card',          // 退款/售后卡片
    LEGO_CARD: '.lego-card',                           // 通用卡片
    IMAGE_MESSAGE: '.image-msg, .video-content',       // 图片/视频消息
    SYSTEM_MESSAGE: '.system-msg',                     // 系统消息
    
    // 转人工相关
    TRANSFER_BUTTON: '.chat-action-transfer, button:contains("转人工")', // 转人工按钮
    TRANSFER_AGENT_LIST: '.agent-list',                // 客服列表
    TRANSFER_AGENT_ITEM: '.agent-item',                // 客服项
    TRANSFER_REASON_BUTTON: '.el-dialog__footer .el-button--primary', // 转人工原因按钮
    TRANSFER_CANCEL_STAR: '.el-button--primary:contains("取消收藏并转移")', // 取消收藏并转移按钮
    
    // 用户信息
    CUSTOMER_NAME: '.base-info span.name',             // 客户名称
    
    // 状态相关
    ONLINE_STATUS: '.status-bar .status',              // 在线状态
    WAIT_TIME_INDICATOR: '.wait-time',                 // 等待时间指示器
    
    // 弹窗相关
    POPUP_DIALOG: '.el-dialog',                        // 弹窗
    POPUP_TITLE: '.el-dialog__title',                  // 弹窗标题
    POPUP_CLOSE: '.el-dialog__close',                  // 弹窗关闭按钮
    POPUP_CONFIRM: '.el-dialog__footer .el-button--primary', // 弹窗确认按钮
    POPUP_CANCEL: '.el-dialog__footer .el-button--default'   // 弹窗取消按钮
  };
  
  // XPath选择器，用于一些复杂的定位
  const XPATH_SELECTORS = {
    // 顶部用户名
    TOP_USERNAME: '/html/body/div[1]/div/div[1]/div[3]/div[1]/div[5]/div[1]/div[1]/div[1]/span[1]',
    // 带有未读标记的聊天项
    UNREAD_CHAT_ITEMS: '//div[@class="chat-portrait"]/i[not(@style="display: none;")]/ancestor::li[@class="chat-item"]',
    // 所有买家消息
    ALL_BUYER_MESSAGES: '//li[contains(@class, "clearfix") and contains(@class, "onemsg") and .//div[ (contains(@class, "buyer") or contains(@class, "lego-card") or contains(@class, "notify-card") ) and not(contains(@class, "system-msg")) ]]',
    // 未收藏标记
    NOT_STAR_FLAG: '//div[@class="base-info"]/i[contains(@class, "unstar")]'
  };
  
  // 导出到命名空间
  window.AIPDD.Platforms.Pinduoduo.Selectors = {
    SELECTORS,
    XPATH_SELECTORS
  };
  
  Logger.debug('PlatformSelectors', '拼多多平台选择器配置已加载');
})(); 