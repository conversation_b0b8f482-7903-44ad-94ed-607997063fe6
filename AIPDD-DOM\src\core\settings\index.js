/**
 * 设置模块入口文件
 * 统一导出设置相关功能
 */

// 确保依赖模块已加载
(function() {
    'use strict';
    
    // 加载设置管理器
    if (!window.AIPDD || !window.AIPDD.Core || !window.AIPDD.Core.Settings) {
        console.error('[Settings] 设置管理器未加载');
    }
    
    // 创建命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Settings = {};
    
    // 获取设置管理器实例
    const SettingsManager = window.AIPDD.Core.Settings.SettingsManager;
    const SETTINGS_TYPES = window.AIPDD.Core.Settings.SETTINGS_TYPES;
    
    // 便捷方法
    const SettingsAPI = {
        // 设置类型
        TYPES: SETTINGS_TYPES,
        
        // 基本设置操作
        get: async function(type, defaultValue = {}) {
            const settings = await SettingsManager.getSettings(type);
            return settings || defaultValue;
        },
        
        save: async function(type, settings) {
            return await SettingsManager.saveSettings(type, settings);
        },
        
        reset: async function(type) {
            return await SettingsManager.resetSettings(type);
        },
        
        // 专用设置区域
        transfer: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.TRANSFER);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.TRANSFER, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.TRANSFER);
            }
        },
        
        ai: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.AI);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.AI, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.AI);
            }
        },
        
        ui: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.UI);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.UI, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.UI);
            }
        },
        
        notification: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.NOTIFICATION);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.NOTIFICATION, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.NOTIFICATION);
            }
        },
        
        automation: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.AUTOMATION);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.AUTOMATION, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.AUTOMATION);
            }
        },
        
        other: {
            get: async function() {
                return await SettingsManager.getSettings(SETTINGS_TYPES.OTHER);
            },
            save: async function(settings) {
                return await SettingsManager.saveSettings(SETTINGS_TYPES.OTHER, settings);
            },
            reset: async function() {
                return await SettingsManager.resetSettings(SETTINGS_TYPES.OTHER);
            }
        },
        
        // 兼容旧版API
        getTransferSettings: async function() {
            return await this.transfer.get();
        },
        
        saveTransferSettings: async function(settings) {
            return await this.transfer.save(settings);
        },
        
        getAISettings: async function() {
            return await this.ai.get();
        },
        
        saveAISettings: async function(settings) {
            return await this.ai.save(settings);
        },
        
        // 初始化方法
        init: async function() {
            console.log('[Settings] 设置模块初始化');
            await SettingsManager.init();
            
            // 创建全局兼容别名
            window.StateManager = window.StateManager || {};
            
            // 兼容旧版StateManager API
            if (!window.StateManager.getState) {
                window.StateManager.getState = async function(key, defaultValue) {
                    // 根据键名映射到对应的设置类型
                    if (key === 'transferSettings') {
                        return await SettingsAPI.transfer.get();
                    } else if (key === 'aiSettings') {
                        return await SettingsAPI.ai.get();
                    } else if (key === 'autoReplyEnabled') {
                        const settings = await SettingsAPI.ai.get();
                        return settings.autoReplyEnabled || defaultValue;
                    } else {
                        // 对于其他键，尝试从存储中获取
                        if (window.AIPDD.Core.Storage) {
                            return await window.AIPDD.Core.Storage.get(key, defaultValue);
                        }
                        return defaultValue;
                    }
                };
            }
            
            if (!window.StateManager.setState) {
                window.StateManager.setState = async function(key, value) {
                    // 根据键名映射到对应的设置类型
                    if (key === 'transferSettings') {
                        return await SettingsAPI.transfer.save(value);
                    } else if (key === 'aiSettings') {
                        return await SettingsAPI.ai.save(value);
                    } else if (key === 'autoReplyEnabled') {
                        const settings = await SettingsAPI.ai.get();
                        settings.autoReplyEnabled = value;
                        return await SettingsAPI.ai.save(settings);
                    } else {
                        // 对于其他键，尝试保存到存储中
                        if (window.AIPDD.Core.Storage) {
                            return await window.AIPDD.Core.Storage.set(key, value);
                        }
                        return false;
                    }
                };
            }
            
            return this;
        }
    };
    
    // 导出到全局命名空间
    window.AIPDD.Settings = SettingsAPI;
    
    console.log('[Settings] 设置模块加载完成');
})(); 