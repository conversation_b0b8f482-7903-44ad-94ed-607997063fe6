/**
 * 平台接口定义
 * 定义所有平台需要实现的方法和属性
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.Platforms = window.AIPDD.Platforms || {};

(function() {
    'use strict';
    
    // 获取Logger，如果已加载
    const Logger = window.AIPDD && window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug ? 
        window.AIPDD.Core.Utils.Debug : 
        { log: console.log.bind(console, '[Platform]') };
    
    /**
     * 平台接口类
     * 所有平台实现必须继承此类并实现所有方法
     */
    class PlatformInterface {
        /**
         * 构造函数
         */
        constructor() {
            this.name = 'BasePlatform';
            this.version = '1.0.0';
            this.isInitialized = false;
            
            // 绑定方法
            this.init = this.init.bind(this);
            this.isSupported = this.isSupported.bind(this);
        }
        
        /**
         * 初始化平台
         * @returns {Promise<boolean>} 初始化是否成功
         */
        async init() {
            Logger.log(`${this.name} 平台初始化`);
            this.isInitialized = true;
            return true;
        }
        
        /**
         * 检查当前页面是否支持此平台
         * @returns {boolean} 是否支持
         */
        isSupported() {
            return false;
        }
        
        /**
         * 获取当前会话ID
         * @param {Element} chatItem - 聊天项元素
         * @returns {string} 会话ID
         */
        getConversationId(chatItem) {
            throw new Error('未实现 getConversationId 方法');
        }
        
        /**
         * 获取客户名称
         * @returns {string} 客户名称
         */
        getCustomerName() {
            throw new Error('未实现 getCustomerName 方法');
        }
        
        /**
         * 获取顶部用户名
         * @returns {string} 用户名
         */
        getTopUsername() {
            throw new Error('未实现 getTopUsername 方法');
        }
        
        /**
         * 获取商品名称
         * @returns {string} 商品名称
         */
        getProductName() {
            throw new Error('未实现 getProductName 方法');
        }
        
        /**
         * 切换到指定对话
         * @param {Element} chatItem - 聊天项元素
         * @returns {Promise<boolean>} 是否切换成功
         */
        async switchToChat(chatItem) {
            throw new Error('未实现 switchToChat 方法');
        }
        
        /**
         * 发送消息
         * @param {string} text - 消息文本
         * @returns {Promise<boolean>} 是否发送成功
         */
        async sendMessage(text) {
            throw new Error('未实现 sendMessage 方法');
        }
        
        /**
         * 查找转人工按钮
         * @returns {Promise<Element|null>} 转人工按钮元素
         */
        async findTransferButton() {
            throw new Error('未实现 findTransferButton 方法');
        }
        
        /**
         * 选择目标客服
         * @param {string} selectedAgent - 指定的客服账号
         * @returns {Promise<Element|null>} 客服元素
         */
        async selectTargetAgent(selectedAgent) {
            throw new Error('未实现 selectTargetAgent 方法');
        }
        
        /**
         * 查找转人工原因按钮
         * @returns {Promise<Element|null>} 转人工原因按钮元素
         */
        async findTransferReasonButton() {
            throw new Error('未实现 findTransferReasonButton 方法');
        }
        
        /**
         * 查找取消收藏按钮
         * @returns {Promise<Element|null>} 取消收藏按钮元素
         */
        async findCancelStarButton() {
            throw new Error('未实现 findCancelStarButton 方法');
        }
        
        /**
         * 查找确认按钮
         * @returns {Promise<Element|null>} 确认按钮元素
         */
        async findConfirmButton() {
            throw new Error('未实现 findConfirmButton 方法');
        }
        
        /**
         * 检查在线状态
         * @returns {Promise<boolean>} 是否在线
         */
        async checkOnlineStatus() {
            throw new Error('未实现 checkOnlineStatus 方法');
        }
        
        /**
         * 获取最新消息
         * @param {string} conversationId - 会话ID
         * @returns {Promise<Object|null>} 消息对象
         */
        async getLatestMessage(conversationId) {
            throw new Error('未实现 getLatestMessage 方法');
        }
        
        /**
         * 标记消息为已回复
         * @param {Element} messageElement - 消息元素
         * @param {string} conversationId - 会话ID
         * @returns {Promise<boolean>} 是否标记成功
         */
        async markMessageAsReplied(messageElement, conversationId) {
            throw new Error('未实现 markMessageAsReplied 方法');
        }
        
        /**
         * 添加可视化回复标记
         * @param {Element} messageElement - 消息元素
         * @returns {Promise<boolean>} 是否添加成功
         */
        async addVisualReplyMark(messageElement) {
            throw new Error('未实现 addVisualReplyMark 方法');
        }
        
        /**
         * 检查消息是否已回复
         * @param {Element} messageElement - 消息元素
         * @param {string} conversationId - 会话ID
         * @returns {Promise<boolean>} 是否已回复
         */
        async hasReplyMark(messageElement, conversationId) {
            throw new Error('未实现 hasReplyMark 方法');
        }
        
        /**
         * 检查未读消息
         * @returns {Promise<Array<Element>>} 未读消息元素数组
         */
        async checkUnreadMessages() {
            throw new Error('未实现 checkUnreadMessages 方法');
        }
        
        /**
         * 从DOM元素中提取消息内容
         * @param {Element} element - 消息元素
         * @returns {string} 消息内容
         */
        extractMessageContent(element) {
            throw new Error('未实现 extractMessageContent 方法');
        }
        
        /**
         * 获取商品卡片信息
         * @param {Element} element - 消息元素
         * @returns {Object|boolean} 商品信息对象或false
         */
        getProductCardInfo(element) {
            throw new Error('未实现 getProductCardInfo 方法');
        }
        
        /**
         * 判断消息类型
         * @param {Object} message - 消息对象
         * @returns {string} 消息类型
         */
        determineMessageType(message) {
            throw new Error('未实现 determineMessageType 方法');
        }
    }
    
    // 导出到命名空间
    window.AIPDD.Platforms.PlatformInterface = PlatformInterface;
    
    Logger.log('平台接口定义已加载');
})(); 