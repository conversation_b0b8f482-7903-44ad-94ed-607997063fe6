/**
 * 拼多多平台实现
 * 实现平台接口定义的所有方法
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.Platforms = window.AIPDD.Platforms || {};
window.AIPDD.Platforms.Pinduoduo = window.AIPDD.Platforms.Pinduoduo || {};

(function() {
    'use strict';
    
    // 获取依赖
    const PlatformInterface = window.AIPDD.Platforms.PlatformInterface;
    const Selectors = window.AIPDD.Platforms.Pinduoduo.Selectors;
    const MessageTypes = window.AIPDD.MessageTypes;
    const EventBus = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.EventBus;
    
    // 获取Logger，如果已加载
    const debug = window.AIPDD && window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug ? 
        window.AIPDD.Core.Utils.Debug.log : 
        console.log.bind(console, '[PinduoduoPlatform]');
    
    /**
     * 拼多多平台实现类
     */
    class PinduoduoPlatform extends PlatformInterface {
        /**
         * 构造函数
         */
        constructor() {
            super();
            this.name = 'PinduoduoPlatform';
            this.version = '1.0.0';
            
            // 平台特定状态
            this.conversationStates = new Map();
            
            // 绑定方法
            this.isSupported = this.isSupported.bind(this);
            this.getConversationId = this.getConversationId.bind(this);
            this.getCustomerName = this.getCustomerName.bind(this);
            this.getTopUsername = this.getTopUsername.bind(this);
            this.switchToChat = this.switchToChat.bind(this);
            this.sendMessage = this.sendMessage.bind(this);
            this.findTransferButton = this.findTransferButton.bind(this);
            this.selectTargetAgent = this.selectTargetAgent.bind(this);
        }
        
        /**
         * 初始化平台
         * @returns {Promise<boolean>} 初始化是否成功
         */
        async init() {
            debug(`${this.name} 平台初始化`);
            
            // 检查依赖
            if (!Selectors) {
                debug('错误: 缺少选择器配置');
                return false;
            }
            
            // 注册事件监听
            if (EventBus) {
                EventBus.on('platform:ready', () => {
                    debug('平台就绪事件触发');
                });
            }
            
            this.isInitialized = true;
            return true;
        }
        
        /**
         * 检查当前页面是否支持此平台
         * @returns {boolean} 是否支持
         */
        isSupported() {
            const url = window.location.href;
            return url.includes('mms.pinduoduo.com/chat-merchant') || 
                   url.includes('mms.pinduoduo.com/mms-chat');
        }
        
        /**
         * 获取当前会话ID
         * @param {Element} chatItem - 聊天项元素
         * @returns {string} 会话ID
         */
        getConversationId(chatItem) {
            try {
                // 首先尝试从chat-item本身获取data-random属性
                let conversationId = chatItem?.getAttribute('data-random');
                
                if (!conversationId) {
                    // 如果chat-item上没有，尝试从chat-item-box获取
                    const chatItemBox = chatItem?.querySelector('.chat-item-box');
                    conversationId = chatItemBox?.getAttribute('data-random');
                }
                
                if (!conversationId) {
                    // 如果还是没有，尝试从父元素获取
                    const parentWithRandom = chatItem?.closest('[data-random]');
                    conversationId = parentWithRandom?.getAttribute('data-random');
                }

                // 如果找到了data-random属性，从中提取数字部分作为用户ID
                if (conversationId) {
                    // 尝试提取数字ID部分（例如从"6484435977599-0-unTimeout"中提取"6484435977599"）
                    const numberId = this.extractNumericUserId(conversationId);
                    if (numberId) {
                        debug('从data-random属性提取到用户ID:', numberId);
                        return numberId;
                    }
                }

                if (!conversationId) {
                    // 如果还是没有，尝试从id属性中提取
                    const id = chatItem?.id;
                    if (id?.includes('_')) {
                        const parts = id.split('_');
                        conversationId = parts[parts.length - 1];
                    }
                }
                
                if (!conversationId) {
                    debug('无法获取会话ID，chatItem:', chatItem);
                    // 生成一个临时ID
                    return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                }
                
                return conversationId;
            } catch (error) {
                debug('获取会话ID时出错:', error);
                // 生成一个临时ID
                return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
        }
        
        /**
         * 从data-random属性中提取数字用户ID
         * @param {string} dataRandomValue - data-random属性值
         * @returns {string|null} 数字用户ID
         */
        extractNumericUserId(dataRandomValue) {
            if (!dataRandomValue) return null;
            
            // 尝试匹配数字部分（如"6484435977599-0-unTimeout"中的"6484435977599"）
            const match = dataRandomValue.match(/^(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
            
            return null;
        }
        
        /**
         * 获取客户名称
         * @returns {string} 客户名称
         */
        getCustomerName() {
            const nameElement = document.evaluate(
                Selectors.XPATH_SELECTORS.TOP_USERNAME,
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            ).singleNodeValue;

            const nameEndingElement = document.evaluate(
                '/html/body/div[1]/div/div[1]/div[2]/div[1]/span[2]',
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            ).singleNodeValue;

            return `${nameElement?.textContent || ''}${nameEndingElement?.textContent || ''}`;
        }
        
        /**
         * 获取顶部用户名
         * @returns {string} 用户名
         */
        getTopUsername() {
            try {
                const usernameElement = document.querySelector('.chat-user-name');
                return usernameElement ? usernameElement.textContent.trim() : 'unknown';
            } catch (error) {
                debug('获取顶部用户名时出错:', error);
                return 'unknown';
            }
        }
        
        /**
         * 获取商品名称
         * @returns {string} 商品名称
         */
        getProductName() {
            const productElement = document.evaluate(
                '((//ul/li/div[@class="cs-item isread" or @class="cs-item unread"])[last()]/ancestor::li[@class="clearfix onemsg"]/following-sibling::li/div[@class="notify-card"]//p)[last()]',
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            ).singleNodeValue;

            return productElement?.textContent || '';
        }
        
        /**
         * 切换到指定对话
         * @param {Element} chatItem - 聊天项元素
         * @returns {Promise<boolean>} 是否切换成功
         */
        async switchToChat(chatItem) {
            try {
                const chatItemBox = chatItem.querySelector('.chat-item-box');
                if (!chatItemBox) {
                    debug('未找到chat-item-box元素');
                    return false;
                }

                // 创建并触发鼠标事件
                const mousedownEvent = new MouseEvent('mousedown', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });

                const mouseupEvent = new MouseEvent('mouseup', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });

                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });

                // 按顺序触发事件
                chatItemBox.dispatchEvent(mousedownEvent);
                await this.sleep(50);
                chatItemBox.dispatchEvent(mouseupEvent);
                await this.sleep(50);
                chatItemBox.dispatchEvent(clickEvent);

                // 等待对话加载
                await this.sleep(1000);

                // 验证是否切换成功
                return chatItemBox.classList.contains('active');
            } catch (error) {
                debug('切换对话失败:', error);
                return false;
            }
        }
        
        /**
         * 发送消息
         * @param {string} text - 消息文本
         * @returns {Promise<boolean>} 是否发送成功
         */
        async sendMessage(text) {
            try {
                // 获取输入框和发送按钮
                const inputBox = document.querySelector(Selectors.SELECTORS.CHAT_INPUT);
                const sendButton = document.querySelector(Selectors.SELECTORS.SEND_BUTTON);
                
                if (!inputBox || !sendButton) {
                    debug('找不到输入框或发送按钮');
                    return false;
                }
                
                // 设置输入框的值
                inputBox.value = text;
                
                // 触发输入事件
                inputBox.dispatchEvent(new Event('input', { bubbles: true }));
                
                // 点击发送按钮
                sendButton.click();
                
                return true;
            } catch (error) {
                debug('发送消息时出错:', error);
                return false;
            }
        }
        
        /**
         * 查找转人工按钮
         * @returns {Promise<Element|null>} 转人工按钮元素
         */
        async findTransferButton() {
            debug('[转人工] 开始查找转移按钮');
            
            // 定义所有可能的选择器
            const selectors = [
                '.transfer-chat-wrap',
                '.transfer-chat',
                '.transfer-btn',
                'span[data-v-309797d7]',
                'button:has(span:contains("转移"))',
                '.el-button:has(span:contains("转移"))',
                '[role="button"]:has(span:contains("转移"))'
            ];
            
            // 等待按钮出现
            for (let i = 0; i < 15; i++) {
                for (const selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        // 检查元素是否可见且可点击
                        if (element && 
                            element.offsetParent !== null && 
                            !element.disabled &&
                            (element.textContent?.includes('转移') || selector === '.transfer-chat-wrap' || selector === '.transfer-chat')) {
                            debug('[转人工] 找到转移按钮:', selector);
                            return element;
                        }
                    }
                }
                await this.sleep(500);
            }
            
            debug('[转人工] 未找到转移按钮');
            return null;
        }
        
        /**
         * 选择目标客服
         * @param {string} selectedAgent - 指定的客服账号
         * @returns {Promise<Element|null>} 客服元素
         */
        async selectTargetAgent(selectedAgent) {
            debug('[转人工] 开始查找目标客服');
            
            // 初始化匹配的转移按钮数组
            window.matchedTransferButtons = [];
            
            // 等待对话框出现
            for (let i = 0; i < 15; i++) { // 增加等待时间
                // 尝试多种可能的对话框选择器
                const dialogs = document.querySelectorAll('.el-dialog.el-dialog--center.tranform-chat-dialog, .el-dialog__wrapper:not(.hide) .el-dialog, .tranform-chat-dialog');
                let dialog = null;
                
                // 查找可见的对话框
                for (const d of dialogs) {
                    if (d && d.offsetParent !== null && getComputedStyle(d).display !== 'none') {
                        dialog = d;
                        break;
                    }
                }
                
                if (dialog) {
                    debug('[转人工] 找到客服对话框');
                    
                    // 先在搜索框中输入指定客服名称
                    if (selectedAgent) {
                        // 尝试多种可能的搜索框选择器
                        const searchInputs = dialog.querySelectorAll('input[placeholder*="请输入"], input[type="text"], .el-input__inner');
                        let searchInput = null;
                        
                        for (const input of searchInputs) {
                            if (input && input.offsetParent !== null) {
                                searchInput = input;
                                break;
                            }
                        }
                        
                        if (searchInput) {
                            debug('[转人工] 在搜索框中输入指定客服:', selectedAgent);
                            // 先清空搜索框
                            searchInput.value = '';
                            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);
                            
                            // 设置新值并触发事件
                            searchInput.value = selectedAgent;
                            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                            searchInput.dispatchEvent(new Event('change', { bubbles: true }));
                            
                            // 等待搜索结果
                            await this.sleep(1500);
                        } else {
                            debug('[转人工] 未找到搜索框');
                        }
                    }

                    // 获取所有可用的转移按钮和对应的客服名称 - 使用多种选择器
                    const transferRows = dialog.querySelectorAll('.el-table__body .el-table__row, .el-table tr, tr');
                    if (transferRows.length > 0) {
                        debug('[转人工] 找到可用的客服行:', transferRows.length);
                        
                        // 查找匹配的客服
                        debug('[转人工] 正在查找指定的客服:', selectedAgent);
                        
                        // 遍历所有客服行，查找匹配的客服名称
                        for (const kefu_row of transferRows) {
                            try {
                                // 获取客服名称，尝试多种可能的选择器
                                let agentName = null;
                                const possibleCells = [
                                    kefu_row.querySelector(".el-table_1_column_1 .cell"),
                                    kefu_row.querySelector(".cell"),
                                    kefu_row.querySelector("td"),
                                    kefu_row.querySelector("div")
                                ];
                                
                                for (const cell of possibleCells) {
                                    if (cell && cell.textContent) {
                                        agentName = cell.textContent.trim();
                                        if (agentName) break;
                                    }
                                }
                                
                                if (!agentName) {
                                    agentName = kefu_row.textContent.trim();
                                }
                                
                                const matchResult = agentName && selectedAgent && agentName.includes(selectedAgent);
                                debug('[转人工] 正在匹配客服:', {
                                    指定客服: selectedAgent,
                                    当前客服: agentName,
                                    匹配结果: matchResult
                                });
                                
                                if (matchResult) {
                                    debug('[转人工] 找到指定的客服:', agentName);
                                    
                                    // 查找该行中的转移按钮
                                    const transferBtn = kefu_row.querySelector('.item-btn-transfer:not(.disabled), button, [role="button"], .el-button');
                                    if (transferBtn) {
                                        debug('[转人工] 找到客服行中的转移按钮');
                                        // 不立即返回，而是收集起来，等找完所有匹配的再随机选择一个
                                        if (!window.matchedTransferButtons) {
                                            window.matchedTransferButtons = [];
                                        }
                                        window.matchedTransferButtons.push({
                                            button: transferBtn,
                                            agentName: agentName
                                        });
                                    } else {
                                        debug('[转人工] 在客服行中未找到转移按钮，尝试使用整行作为点击目标');
                                        if (!window.matchedTransferButtons) {
                                            window.matchedTransferButtons = [];
                                        }
                                        window.matchedTransferButtons.push({
                                            button: kefu_row,
                                            agentName: agentName
                                        });
                                    }
                                }
                            } catch (error) {
                                debug('[转人工] 处理客服行时出错:', error);
                            }
                        }
                        
                        // 如果找到了匹配的客服，随机选择一个
                        if (window.matchedTransferButtons && window.matchedTransferButtons.length > 0) {
                            const randomIndex = Math.floor(Math.random() * window.matchedTransferButtons.length);
                            const selected = window.matchedTransferButtons[randomIndex];
                            debug('[转人工] 随机选择客服:', selected.agentName);
                            return selected.button;
                        }
                        
                        // 如果没有找到完全匹配的客服，但有可用的客服，随机选择一个
                        debug('[转人工] 未找到完全匹配的客服，随机选择一个可用客服');
                        const randomRowIndex = Math.floor(Math.random() * transferRows.length);
                        const randomRow = transferRows[randomRowIndex];
                        const transferBtn = randomRow.querySelector('.item-btn-transfer:not(.disabled), button, [role="button"], .el-button');
                        
                        if (transferBtn) {
                            debug('[转人工] 找到随机客服行中的转移按钮');
                            return transferBtn;
                        } else {
                            debug('[转人工] 在随机客服行中未找到转移按钮，尝试使用整行作为点击目标');
                            return randomRow;
                        }
                    } else {
                        debug('[转人工] 未找到可用的客服行');
                    }
                }
                
                await this.sleep(500);
            }
            
            debug('[转人工] 未找到客服对话框或客服');
            return null;
        }
        
        /**
         * 查找转人工原因按钮
         * @returns {Promise<Element|null>} 转人工原因按钮元素
         */
        async findTransferReasonButton() {
            // 等待按钮出现
            for (let i = 0; i < 10; i++) {
                // 尝试多种可能的选择器
                const buttons = document.querySelectorAll('.el-dialog__footer .el-button--primary, .el-button--primary:contains("确定"), button:contains("确定")');
                
                for (const button of buttons) {
                    if (button && 
                        button.offsetParent !== null && 
                        !button.disabled &&
                        button.textContent?.includes('确定')) {
                        debug('[转人工] 找到转移原因按钮');
                        return button;
                    }
                }
                
                await this.sleep(500);
            }
            
            debug('[转人工] 未找到转移原因按钮');
            return null;
        }
        
        /**
         * 查找取消收藏按钮
         * @returns {Promise<Element|null>} 取消收藏按钮元素
         */
        async findCancelStarButton() {
            // 等待按钮出现
            for (let i = 0; i < 10; i++) {
                // 尝试多种可能的选择器
                const buttons = document.querySelectorAll('.el-button--primary:contains("取消收藏并转移"), button:contains("取消收藏并转移"), .el-button--primary');
                
                for (const button of buttons) {
                    if (button && 
                        button.offsetParent !== null && 
                        !button.disabled &&
                        (button.textContent?.includes('取消收藏并转移') || button.textContent?.includes('确定'))) {
                        debug('[转人工] 找到取消收藏按钮或确认按钮');
                        return button;
                    }
                }
                
                await this.sleep(500);
            }
            
            debug('[转人工] 未找到取消收藏按钮');
            return null;
        }
        
        /**
         * 查找确认按钮
         * @returns {Promise<Element|null>} 确认按钮元素
         */
        async findConfirmButton() {
            // 等待按钮出现
            for (let i = 0; i < 10; i++) {
                // 尝试多种可能的选择器
                const buttons = document.querySelectorAll('.el-button--primary:contains("确定"), button:contains("确定"), .el-button--primary');
                
                for (const button of buttons) {
                    if (button && 
                        button.offsetParent !== null && 
                        !button.disabled &&
                        button.textContent?.includes('确定')) {
                        debug('[转人工] 找到确认按钮');
                        return button;
                    }
                }
                
                await this.sleep(500);
            }
            
            debug('[转人工] 未找到确认按钮');
            return null;
        }
        
        /**
         * 等待指定时间
         * @param {number} ms - 等待时间（毫秒）
         * @returns {Promise<void>}
         */
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }
    
    // 创建实例并导出到命名空间
    const instance = new PinduoduoPlatform();
    window.AIPDD.Platforms.Pinduoduo.PinduoduoPlatform = instance;
    
    // 如果当前页面支持此平台，自动初始化
    if (instance.isSupported()) {
        instance.init().then(() => {
            debug('拼多多平台自动初始化完成');
            
            // 触发平台就绪事件
            if (EventBus) {
                EventBus.emit('platform:ready', { platform: instance });
            }
        }).catch(error => {
            debug('拼多多平台初始化失败:', error);
        });
    }
    
    debug('拼多多平台模块已加载');
})(); 