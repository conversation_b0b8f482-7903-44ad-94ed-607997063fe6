// Debug logging
function debug(...args) {
    console.log('[Background]', ...args);
}

// 版本号
const VERSION = '2024.12.13 02:06';

// Dify API 配置
const DIFY_CONFIG = {
    apiUrl: 'http://ip.aijiakefu.com/v1',
    apiKey: ''  // 生效的 key   默认app-E1lXvvLlpNDqooo36ovkgFPd   备用key   app-OzDjbYof039ygKc6GUhCoiIh   综合测试 app-irHBq0ruNKZCpc7AMZNKS6dS
};

// 当前使用的配置（初始值为默认配置）
let currentDifyConfig = { ...DIFY_CONFIG };


// 从 chrome.storage.local 加载配置
async function loadDifyConfig() {
    try {
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['aiSettings'], resolve);
        });

        if (result.aiSettings && result.aiSettings.apiKey) {
            currentDifyConfig.apiKey = result.aiSettings.apiKey;
            debug('[Background] Loaded apiKey from aiSettings:', currentDifyConfig.apiKey);
        }
    } catch (error) {
        debug('[Background] Error loading apiKey from storage:', error);
    }
}
// 初始化
(async () => {
    await loadDifyConfig(); // 加载配置
})();

// 会话管理
let conversations = new Map();

debug('Initialized with config:', {
    version: VERSION,
    dify: DIFY_CONFIG
});

// 打开设置页面
function openOptionsPage() {
    debug('Opening options page');
    chrome.runtime.openOptionsPage();
}

// 打开关于页面
function openAboutPage() {
    debug('Opening about page');
    chrome.tabs.create({
        url: chrome.runtime.getURL('src/about/about.html')
    });
}

// 打开拼多多商家客服聊天窗口
function openPddChatPage() {
    debug('Opening PDD chat page');
    chrome.tabs.create({
        url: 'https://mms.pinduoduo.com/chat-merchant/index.html#/'
    });
}

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(() => {
    debug('Extension icon clicked');
    openPddChatPage();
});

// 调用 Dify API
async function callDifyAPI(data) {
    debug('Dify API Configuration:', {
        key: currentDifyConfig.apiKey,
    });
    
    if(currentDifyConfig.apiKey == ''){
        throw new Error(`Dify API apiKey is empty`);
    }

    // 获取用户的会话ID
    let fullUserId = data.user || 'default';
    
    // 从格式化的用户ID中提取纯用户ID用于会话合并
    // 格式可能是: "6484435977599_pdd12252958099_***" 或 "6484435977599_1***" 或其他
    let userId = fullUserId;
    
    // 如果包含分隔符"_"，提取前面的部分作为用户ID
    if (fullUserId.includes('_')) {
        userId = fullUserId.split('_')[0];
        debug('Extracted user ID for conversation merging:', userId, 'from full ID:', fullUserId);
    }
    
    //游客则不使用合并会话
    if(userId.includes('游客') || userId.includes('default_user') || fullUserId.includes('游客') || fullUserId.includes('default_user')){
        throw new Error(`游客丢弃会话，等待重新提交`);
    }else {
        // 如果有现有会话ID，使用它
        if (conversations.has(userId)) {
            data.conversation_id = conversations.get(userId);
            debug('Using existing conversation for user:', userId, 'conversation:', data.conversation_id);
        } else {
            // 如果没有会话ID，不传入conversation_id，让Dify API创建新会话
            debug('No existing conversation for user:', userId, 'creating new conversation');
            //尝试 恢复
            let dify_user_conversation = await loadMapFromStorage('dify_user_conversation');

            conversations = dify_user_conversation;
            if(conversations.has(userId)){
                data.conversation_id = conversations.get(userId);
            }
        }
    }
    
    debug('Request Data:', data);
    
    try {
        const requestConfig = {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentDifyConfig.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        };
        debug('Request Configuration:', requestConfig);
        
        const response = await fetch(`${currentDifyConfig.apiUrl}/chat-messages`, requestConfig);

        if (!response.ok) {
            const errorText = await response.text();
            debug('Error Response:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                body: errorText
            });
            throw new Error(`Dify API error: ${response.status} ${errorText}`);
        }

        // 处理流式响应
        if (data.response_mode === 'streaming') {
            const reader = response.body.getReader();
            let chatStr = '';
            let conversationId = null;
            
            while (true) {
                const {done, value} = await reader.read();
                if (done) break;
                
                const chunk = new TextDecoder().decode(value);
                const matches = chunk.match(/data: ({.*})/g);
                
                if (matches) {
                    for (const match of matches) {
                        const jsonStr = match.replace('data: ', '');
                        try {
                            const responseData = JSON.parse(jsonStr);
                            if (responseData.answer) {
                                chatStr += responseData.answer;
                            }
                            if (responseData.conversation_id) {
                                conversationId = responseData.conversation_id;
                            }
                        } catch (e) {
                            debug('JSON parse error:', e);
                        }
                    }
                }
            }
            
            // 保存会话ID
            if (conversationId) {
                conversations.set(userId, conversationId);
                debug('Saved new conversation ID for user:', userId, 'conversation:', conversationId);
                await saveMapToStorage(conversations, 'dify_user_conversation');
            }
            
            return {
                answer: chatStr,
                conversation_id: conversationId
            };
        }
        
        // 处理普通响应
        const responseData = await response.json();
        debug('Response Data:', responseData);
        
        // 保存会话ID
        if (responseData.conversation_id) {
            conversations.set(userId, responseData.conversation_id);
            debug('Saved conversation ID for user:', userId, 'conversation:', responseData.conversation_id);

            await saveMapToStorage(conversations, 'dify_user_conversation');
        }
        
        return responseData;
    } catch (error) {
        debug('API Call Error:', error);
        throw error;
    }
}

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        if (tab.url.startsWith('https://mms.pinduoduo.com/')) {
            debug('Detected PDD MMS page, ready to handle messages');
        }
    }
});

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'getInitialDifyConfig') {
        // 返回当前配置
        sendResponse({ 
            success: true, 
            data: currentDifyConfig 
        });
    }
    else if (request.type === 'updateDifyConfig') {
        // 更新当前配置
        currentDifyConfig = {
            ...currentDifyConfig,
            ...request.data
        };
        //key修改之后，重置会话持久化
        conversations.clear();
        chrome.storage.local.remove('dify_user_conversation');
        debug('Updated Dify config:', currentDifyConfig);
        sendResponse({ success: true });
    }
    else if (request.type === 'difyRequest') {
        const requestId = request.data.requestId || 'unknown';
        
        // 统一处理所有DIFY请求，不再区分商品卡和文本消息
        debug(`[${requestId}] 处理DIFY请求`);
        
        // 使用当前配置处理请求
        callDifyAPI(request.data)
            .then(response => {
                debug(`[${requestId}] DIFY请求已完成，发送响应`);
                
                // 确保响应中包含会话ID
                if (response && response.conversation_id) {
                    debug(`[${requestId}] 响应包含会话ID: ${response.conversation_id}`);
                }
                
                sendResponse({ success: true, data: response });
            })
            .catch(error => {
                debug(`[${requestId}] DIFY请求错误:`, error);
                sendResponse({ success: false, error: error.message });
            });
        return true; // 保持消息通道开启
    }else if (request.type === 'loadLocalFile') {
        // 处理加载本地文件请求
        loadLocalFile(request.filePath, sendResponse);
        return true; // 表示异步响应
    }
    return true;
});

// 加载本地文件
async function loadLocalFile(filePath, sendResponse) {
    debug('[dify]开始加载本地文件:', filePath);
    try {
        // 使用 chrome.runtime.getURL 获取文件的绝对 URL
        const fileUrl = chrome.runtime.getURL(filePath);

        const response = await fetch(fileUrl);
        if (!response.ok) {
            debug('[dify]获取本地文件失败:', response.status, response.statusText);
            sendResponse({ success: false, error: '获取本地文件失败' });
            return;
        }
        const fileData = await response.arrayBuffer();
        debug('[dify]本地文件加载成功:', fileData);
        sendResponse({ success: true, fileData: fileData });
    } catch (error) {
        debug('[dify]加载本地文件失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 将 Map 转换为对象
const mapToObject = (map) => Object.fromEntries(map.entries());

// 存储 Map 到 chrome.storage.local
const saveMapToStorage = async (map, key) => {
    // 过滤掉 key 包含 "游客" 的项
    const filteredMap = new Map();
    for (const [k, v] of map) {
        if (!k.includes('游客')) {
            filteredMap.set(k, v);
        }
    }
    const obj = mapToObject(map);
    await chrome.storage.local.set({ [key]: obj });
    console.log('Map 存储成功:', obj);
};

// 从 chrome.storage.local 恢复 Map
const loadMapFromStorage = async (key) => {
    const result = await chrome.storage.local.get([key]);
    if (result[key]) {
        const map = new Map(Object.entries(result[key]));
        // 过滤掉 key 包含 "游客" 的项
        const filteredMap = new Map();
        for (const [k, v] of map) {
            if (!k.includes('游客')) {
                filteredMap.set(k, v);
            }
        }
        console.log('Map 恢复成功（已过滤游客项）:', filteredMap);
        return filteredMap;
    }
    console.log('未找到存储的 Map');
    return new Map(); // 如果不存在，返回一个新的 Map
};