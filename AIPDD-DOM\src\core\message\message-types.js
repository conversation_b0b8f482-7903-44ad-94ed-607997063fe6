/**
 * 消息类型常量定义
 * 定义系统中使用的所有消息类型
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.MessageTypes = {
    // 基本消息类型
    TEXT: 'text',           // 文本消息
    IMAGE: 'image',         // 图片消息
    PRODUCT: 'product',     // 商品消息
    ORDER: 'order',         // 订单消息
    REFUND: 'refund',       // 退款/售后消息
    LEGOCARD: 'legocard',   // 乐高卡片消息
    EMOJI: 'emoji',         // 表情消息
    UNKNOWN: 'unknown',     // 未知类型消息
    
    // 扩展消息类型
    TRANSFER: 'transfer',   // 转人工消息
    SYSTEM: 'system',       // 系统消息
    NOTIFICATION: 'notification', // 通知消息
    
    // 商品卡片类型
    PRODUCT_CARD_TYPES: {
        STANDARD: 1,        // 标准商品卡
        NOTIFY: 2,          // 通知商品卡
        SPEC: 3             // 规格商品卡
    }
};

// 导出消息类型常量，便于其他模块使用
(function() {
    // 如果在content script环境中，将消息类型挂载到window对象
    if (typeof window !== 'undefined') {
        window.MESSAGE_TYPES = window.AIPDD.MessageTypes;
    }
})();