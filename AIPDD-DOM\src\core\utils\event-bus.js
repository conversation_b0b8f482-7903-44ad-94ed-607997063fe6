/**
 * 事件总线模块
 * 实现模块间松耦合通信
 */
(function() {
  'use strict';

  // 确保命名空间存在
  window.AIPDD = window.AIPDD || {};
  window.AIPDD.Core = window.AIPDD.Core || {};
  window.AIPDD.Core.Utils = window.AIPDD.Core.Utils || {};

  // 依赖检查
  const Debug = window.AIPDD.Core.Utils.Debug;

  /**
   * 事件总线类
   */
  class EventBus {
      constructor() {
          this.events = {};
          this.onceEvents = {};
          
          if (Debug) {
              Debug.log('事件总线已初始化', 'event-bus', 'info');
          }
      }

      /**
       * 注册事件监听器
       * @param {string} event - 事件名称
       * @param {Function} callback - 回调函数
       * @returns {Function} 用于移除监听器的函数
       */
      on(event, callback) {
          if (!event || typeof callback !== 'function') {
              if (Debug) Debug.warn(`无效的事件注册: ${event}`, 'event-bus');
              return () => {};
          }
          
          // 初始化事件数组
          if (!this.events[event]) {
              this.events[event] = [];
          }
          
          // 添加回调
          this.events[event].push(callback);
          
          if (Debug) Debug.debug(`注册事件监听器: ${event}`, 'event-bus');
          
          // 返回移除函数
          return () => this.off(event, callback);
      }

      /**
       * 注册一次性事件监听器
       * @param {string} event - 事件名称
       * @param {Function} callback - 回调函数
       * @returns {Function} 用于移除监听器的函数
       */
      once(event, callback) {
          if (!event || typeof callback !== 'function') {
              if (Debug) Debug.warn(`无效的一次性事件注册: ${event}`, 'event-bus');
              return () => {};
          }
          
          // 初始化一次性事件数组
          if (!this.onceEvents[event]) {
              this.onceEvents[event] = [];
          }
          
          // 添加回调
          this.onceEvents[event].push(callback);
          
          if (Debug) Debug.debug(`注册一次性事件监听器: ${event}`, 'event-bus');
          
          // 返回移除函数
          return () => {
              const index = this.onceEvents[event]?.indexOf(callback);
              if (index !== -1) {
                  this.onceEvents[event].splice(index, 1);
              }
          };
      }

      /**
       * 移除事件监听器
       * @param {string} event - 事件名称
       * @param {Function} [callback] - 回调函数，如不提供则移除所有该事件的监听器
       */
      off(event, callback) {
          if (!event) {
              if (Debug) Debug.warn('尝试移除未指定事件的监听器', 'event-bus');
              return;
          }
          
          // 如果没有提供回调，移除所有该事件的监听器
          if (!callback) {
              delete this.events[event];
              delete this.onceEvents[event];
              if (Debug) Debug.debug(`移除所有 ${event} 事件监听器`, 'event-bus');
              return;
          }
          
          // 从常规事件中移除
          if (this.events[event]) {
              const index = this.events[event].indexOf(callback);
              if (index !== -1) {
                  this.events[event].splice(index, 1);
                  if (Debug) Debug.debug(`移除事件监听器: ${event}`, 'event-bus');
              }
              
              // 如果没有更多监听器，清理事件
              if (this.events[event].length === 0) {
                  delete this.events[event];
              }
          }
          
          // 从一次性事件中移除
          if (this.onceEvents[event]) {
              const index = this.onceEvents[event].indexOf(callback);
              if (index !== -1) {
                  this.onceEvents[event].splice(index, 1);
                  if (Debug) Debug.debug(`移除一次性事件监听器: ${event}`, 'event-bus');
              }
              
              // 如果没有更多监听器，清理事件
              if (this.onceEvents[event].length === 0) {
                  delete this.onceEvents[event];
              }
          }
      }

      /**
       * 触发事件
       * @param {string} event - 事件名称
       * @param {any} data - 事件数据
       * @returns {Promise<boolean>} 是否有监听器处理了该事件
       */
      async emit(event, data) {
          if (!event) {
              if (Debug) Debug.warn('尝试触发未指定名称的事件', 'event-bus');
              return false;
          }
          
          let handled = false;
          const errors = [];
          
          // 处理常规事件
          if (this.events[event] && this.events[event].length > 0) {
              handled = true;
              if (Debug) Debug.debug(`触发事件: ${event}，监听器数量: ${this.events[event].length}`, 'event-bus');
              
              // 异步调用所有监听器
              const promises = this.events[event].map(callback => {
                  try {
                      return Promise.resolve(callback(data));
                  } catch (error) {
                      errors.push(error);
                      if (Debug) Debug.error(`事件处理器出错: ${error.message}`, 'event-bus');
                      return Promise.resolve();
                  }
              });
              
              await Promise.all(promises);
          }
          
          // 处理一次性事件
          if (this.onceEvents[event] && this.onceEvents[event].length > 0) {
              handled = true;
              if (Debug) Debug.debug(`触发一次性事件: ${event}，监听器数量: ${this.onceEvents[event].length}`, 'event-bus');
              
              // 保存回调并清空事件
              const callbacks = [...this.onceEvents[event]];
              delete this.onceEvents[event];
              
              // 异步调用所有监听器
              const promises = callbacks.map(callback => {
                  try {
                      return Promise.resolve(callback(data));
                  } catch (error) {
                      errors.push(error);
                      if (Debug) Debug.error(`一次性事件处理器出错: ${error.message}`, 'event-bus');
                      return Promise.resolve();
                  }
              });
              
              await Promise.all(promises);
          }
          
          // 如果有错误，但不影响返回值
          if (errors.length > 0) {
              if (Debug) Debug.warn(`事件 ${event} 处理过程中有 ${errors.length} 个错误`, 'event-bus');
          }
          
          return handled;
      }

      /**
       * 获取特定事件的监听器数量
       * @param {string} event - 事件名称
       * @returns {number} 监听器数量
       */
      listenerCount(event) {
          if (!event) return 0;
          
          const regularCount = this.events[event]?.length || 0;
          const onceCount = this.onceEvents[event]?.length || 0;
          
          return regularCount + onceCount;
      }

      /**
       * 获取所有已注册的事件名称
       * @returns {string[]} 事件名称数组
       */
      eventNames() {
          const regularEvents = Object.keys(this.events);
          const onceEvents = Object.keys(this.onceEvents);
          
          // 合并并去重
          return [...new Set([...regularEvents, ...onceEvents])];
      }

      /**
       * 移除所有事件监听器
       */
      removeAllListeners() {
          this.events = {};
          this.onceEvents = {};
          
          if (Debug) Debug.debug('移除所有事件监听器', 'event-bus');
      }
  }

  // 导出到命名空间
  window.AIPDD.Core.Utils.EventBus = new EventBus();

  // 调试日志
  if (Debug) {
      Debug.log('事件总线模块已加载', 'core', 'info');
  }
})();
