// 常量配置 - 集中管理系统常量
window.AIPDD = window.AIPDD || {};
window.AIPDD.Config = window.AIPDD.Config || {};

(function() {
  // 应用版本
  const VERSION = '1.0.0';
  
  // 消息类型常量
  const MESSAGE_TYPES = {
    TEXT: 'text',
    PRODUCT: 'product',
    MEDIA: 'media',
    IMAGE: 'image',
    SYSTEM: 'system',
    UNKNOWN: 'unknown'
  };
  
  // 应用状态常量
  const APP_STATUS = {
    IDLE: 'idle',
    RUNNING: 'running',
    STOPPED: 'stopped',
    ERROR: 'error',
    TRANSFER: 'transfer'
  };
  
  // 事件名称
  const EVENTS = {
    // 应用事件
    APP_STARTED: 'app:started',
    APP_STOPPED: 'app:stopped',
    APP_ERROR: 'app:error',
    APP_STATUS_CHANGED: 'app:status:changed',
    
    // 消息事件
    MESSAGE_RECEIVED: 'message:received',
    MESSAGE_PROCESSED: 'message:processed',
    MESSAGE_SENT: 'message:sent',
    
    // 转人工相关
    TRANSFER_STARTED: 'transfer:started',
    TRANSFER_COMPLETED: 'transfer:completed',
    TRANSFER_FAILED: 'transfer:failed',
    
    // UI事件
    UI_FLOATING_BALL_CREATED: 'ui:floatingBall:created',
    UI_SETTINGS_SAVED: 'ui:settings:saved',
    
    // 存储事件
    STORAGE_SETTINGS_CHANGED: 'storage:settings:changed',
    
    // 健康检查
    HEALTH_CHECK_PASSED: 'health:check:passed',
    HEALTH_CHECK_FAILED: 'health:check:failed'
  };
  
  // 存储键名
  const STORAGE_KEYS = {
    SETTINGS: 'aipdd_settings',
    AI_SETTINGS: 'aipdd_ai_settings',
    TRANSFER_KEYWORDS: 'aipdd_transfer_keywords',
    BACKUP_KEYWORDS: 'aipdd_backup_keywords',
    STAR_FLAG_KEYWORDS: 'aipdd_star_flag_keywords',
    SERVICE_ACCOUNTS: 'aipdd_service_accounts',
    REPLIED_MESSAGES: 'aipdd_replied_messages',
    PROCESSED_CONVERSATIONS: 'aipdd_processed_conversations'
  };
  
  // 导出到命名空间
  window.AIPDD.Config.Constants = {
    VERSION,
    MESSAGE_TYPES,
    APP_STATUS,
    EVENTS,
    STORAGE_KEYS
  };
})(); 