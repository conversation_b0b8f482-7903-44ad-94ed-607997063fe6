/**
 * 调试工具模块
 * 提供统一的日志记录系统，支持多级日志
 */
(function() {
    'use strict';

    // 确保命名空间存在
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Core = window.AIPDD.Core || {};
    window.AIPDD.Core.Utils = window.AIPDD.Core.Utils || {};

    // 日志级别定义
    const LogLevels = {
        DEBUG: 0,
        INFO: 1,
        WARN: 2,
        ERROR: 3,
        NONE: 4
    };

    // 默认配置
    const config = {
        enabled: true,
        level: LogLevels.INFO,
        useColors: true,
        showTimestamp: true,
        showModule: true
    };

    /**
     * 调试工具类
     */
    class Debug {
        constructor() {
            this.LogLevels = LogLevels;
            this.config = config;
        }

        /**
         * 设置调试配置
         * @param {Object} options - 配置选项
         */
        configure(options = {}) {
            if (options.enabled !== undefined) this.config.enabled = !!options.enabled;
            if (options.level !== undefined) this.config.level = options.level;
            if (options.useColors !== undefined) this.config.useColors = !!options.useColors;
            if (options.showTimestamp !== undefined) this.config.showTimestamp = !!options.showTimestamp;
            if (options.showModule !== undefined) this.config.showModule = !!options.showModule;
        }

        /**
         * 获取当前时间戳字符串
         * @returns {string} 格式化的时间戳
         */
        getTimestamp() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
            
            return `${hours}:${minutes}:${seconds}.${milliseconds}`;
        }

        /**
         * 获取日志级别对应的样式
         * @param {string} level - 日志级别
         * @returns {Object} 样式对象
         */
        getLevelStyle(level) {
            const styles = {
                debug: 'color: #9E9E9E',
                info: 'color: #2196F3',
                warn: 'color: #FF9800',
                error: 'color: #F44336; font-weight: bold'
            };
            
            return styles[level] || '';
        }

        /**
         * 记录日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {string} [level='info'] - 日志级别
         */
        log(message, module = 'general', level = 'info') {
            // 检查是否启用日志
            if (!this.config.enabled) return;
            
            // 检查日志级别
            const levelValue = this._getLevelValue(level);
            if (levelValue < this.config.level) return;
            
            // 准备日志前缀
            let prefix = '';
            let styles = [];
            
            // 添加时间戳
            if (this.config.showTimestamp) {
                prefix += `[${this.getTimestamp()}] `;
            }
            
            // 添加日志级别
            prefix += `[${level.toUpperCase()}]`;
            
            // 添加模块名称
            if (this.config.showModule && module) {
                prefix += ` [${module}]`;
            }
            
            // 使用彩色日志
            if (this.config.useColors) {
                const levelStyle = this.getLevelStyle(level);
                styles.push(levelStyle);
                console.log(`%c${prefix}`, levelStyle, message);
            } else {
                console.log(prefix, message);
            }
        }

        /**
         * 获取日志级别的数值
         * @param {string} level - 日志级别
         * @returns {number} 级别数值
         * @private
         */
        _getLevelValue(level) {
            switch (level.toLowerCase()) {
                case 'debug': return LogLevels.DEBUG;
                case 'info': return LogLevels.INFO;
                case 'warn': return LogLevels.WARN;
                case 'error': return LogLevels.ERROR;
                default: return LogLevels.INFO;
            }
        }

        /**
         * 调试级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         */
        debug(message, module = 'general') {
            this.log(message, module, 'debug');
        }

        /**
         * 信息级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         */
        info(message, module = 'general') {
            this.log(message, module, 'info');
        }

        /**
         * 警告级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         */
        warn(message, module = 'general') {
            this.log(message, module, 'warn');
        }

        /**
         * 错误级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         */
        error(message, module = 'general') {
            this.log(message, module, 'error');
        }
    }

    // 导出到命名空间
    window.AIPDD.Core.Utils.Debug = new Debug();

    // 初始日志
    window.AIPDD.Core.Utils.Debug.log('调试工具模块已加载', 'core', 'info');
})();