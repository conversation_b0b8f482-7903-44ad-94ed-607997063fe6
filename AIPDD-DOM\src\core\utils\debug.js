// debug.js - 调试工具模块
(function() {
    // 命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Utils = window.AIPDD.Utils || {};
    
    // 调试工具
    const DebugUtils = {
        /**
         * 调试日志输出
         * @param {...any} args - 日志参数
         */
        debug: function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}]`, ...args);
        },
        
        /**
         * 获取当前时间
         * @returns {string} 当前时间字符串
         */
        getCurrentTime: function() {
            return new Date().toLocaleTimeString();
        },
        
        /**
         * 获取当前时间戳
         * @returns {number} 时间戳
         */
        getTimestamp: function() {
            return Date.now();
        },
        
        /**
         * 格式化时间戳
         * @param {number} timestamp - 时间戳
         * @returns {string} 格式化的时间字符串
         */
        formatTimestamp: function(timestamp) {
            return new Date(timestamp).toLocaleTimeString();
        },
        
        /**
         * 延迟执行
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        sleep: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        
        /**
         * 等待元素出现
         * @param {string} selector - 选择器
         * @param {number} timeout - 超时时间（毫秒）
         * @param {Element} parent - 父元素
         * @returns {Promise<Element|null>} 元素或null
         */
        waitForElement: function(selector, timeout = 5000, parent = document) {
            return new Promise((resolve) => {
                const element = parent.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                const observer = new MutationObserver((mutations) => {
                    const element = parent.querySelector(selector);
                    if (element) {
                        observer.disconnect();
                        resolve(element);
                    }
                });
                
                observer.observe(parent, {
                    childList: true,
                    subtree: true
                });
                
                // 超时处理
                setTimeout(() => {
                    observer.disconnect();
                    resolve(null);
                }, timeout);
            });
        },
        
        /**
         * 检查元素是否可见
         * @param {Element} element - 要检查的元素
         * @returns {boolean} 是否可见
         */
        isElementVisible: function(element) {
            if (!element) return false;
            
            const style = window.getComputedStyle(element);
            return style.display !== 'none' && 
                   style.visibility !== 'hidden' && 
                   style.opacity !== '0' &&
                   element.offsetWidth > 0 && 
                   element.offsetHeight > 0;
        },
        
        /**
         * 生成随机ID
         * @param {number} length - ID长度
         * @returns {string} 随机ID
         */
        generateId: function(length = 8) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        },
        
        /**
         * 深度克隆对象
         * @param {any} obj - 要克隆的对象
         * @returns {any} 克隆后的对象
         */
        deepClone: function(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const cloned = {};
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        cloned[key] = this.deepClone(obj[key]);
                    }
                }
                return cloned;
            }
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Utils.Debug = DebugUtils;
    
    // 为了向下兼容，将debug函数暴露到全局
    window.debug = DebugUtils.debug;
    window.getCurrentTime = DebugUtils.getCurrentTime;
    window.sleep = DebugUtils.sleep;
    window.waitForElement = DebugUtils.waitForElement;
    window.isElementVisible = DebugUtils.isElementVisible;
})();
