// transfer-rules.js
(function() {
    // 依赖
    const Settings = window.AIPDD.Settings;
    
    // 消息类型常量
    const MESSAGE_TYPES = {
        TEXT: 'text',
        IMAGE: 'image',
        REFUND: 'refund'
    };
    
    // 转人工状态常量
    const TransferState = {
        NONE: 0,
        TRANSFERRING: 1,
        TRANSFERRED: 2,
        FAILED: 3
    };
    
    // 转人工规则
    const TransferRules = {
        /**
         * 检查是否需要转人工
         * @param {Object} message - 消息对象
         * @param {Object} state - 会话状态（可选）
         * @returns {Promise<Object>} 检查结果
         */
        shouldTransfer: async function(message, state) {
            try {
                // 获取设置
                const settings = await Settings.getTransferSettings();

                // 检查是否包含转人工关键词
                const isTransferKeyword = message.content &&
                    (message.content.includes('转人工') ||
                     message.content.includes('转接') ||
                     message.content.includes('转售前') ||
                     message.content.includes('转售后'));

                // 首先检查总开关状态
                if (!settings.manualEnabled) {
                    // 只有当消息包含转人工关键词时才处理
                    if (isTransferKeyword) {
                        return { shouldTransfer: false, reason: 'closed' };
                    }
                    return { shouldTransfer: false, reason: 'disabled' };
                }

                // 只有在总开关开启的情况下，才检查各个子开关
                if (settings.manualEnabled) {
                    // 关键词触发转人工
                    if (settings.keywordEnabled && message.content) {
                        const keywords = await Settings.getTransferKeywords();
                        if (keywords.some(keyword => message.content.includes(keyword))) {
                            console.log('[TransferRules] 检测到转人工关键词，触发转人工');
                            return { shouldTransfer: true, reason: 'keyword' };
                        }
                    }

                    // 图片消息转人工
                    if (settings.imageEnabled && message.type === MESSAGE_TYPES.IMAGE) {
                        console.log('[TransferRules] 检测到图片消息，触发转人工');
                        return { shouldTransfer: true, reason: 'image' };
                    }

                    // 退款场景转人工
                    if (settings.refundEnabled && message.type === MESSAGE_TYPES.REFUND) {
                        console.log('[TransferRules] 检测到退款相关内容，触发转人工');
                        return { shouldTransfer: true, reason: 'refund' };
                    }

                    // 投诉场景转人工
                    if (settings.complaintEnabled && this._isComplaintMessage(message)) {
                        console.log('[TransferRules] 检测到投诉相关内容，触发转人工');
                        return { shouldTransfer: true, reason: 'complaint' };
                    }
                }

                return { shouldTransfer: false, reason: 'no_match' };
            } catch (error) {
                console.error('[TransferRules] 检查转人工规则时出错:', error);
                return { shouldTransfer: false, reason: 'error' };
            }
        },

        /**
         * 兼容API: checkShouldTransfer
         * @param {Object} message - 消息对象
         * @param {Object} state - 会话状态
         * @returns {Promise<boolean>} 是否需要转人工
         */
        checkShouldTransfer: async function(message, state) {
            const result = await this.shouldTransfer(message, state);
            return result.shouldTransfer;
        },

        /**
         * 检查是否是投诉消息
         * @param {Object} message - 消息对象
         * @returns {boolean} 是否是投诉消息
         */
        _isComplaintMessage: function(message) {
            if (!message.content) return false;

            const complaintKeywords = [
                '投诉', '举报', '差评', '不满意', '服务态度', '质量问题',
                '欺骗', '虚假', '骗人', '坑人', '垃圾', '太差了'
            ];

            return complaintKeywords.some(keyword => message.content.includes(keyword));
        }
    
                // 关键词触发转人工
                if (settings.keywordEnabled && message.content) {
                    const keywords = await Settings.getTransferKeywords();
                    if (keywords.some(keyword => message.content.includes(keyword))) {
                        return { shouldTransfer: true, reason: 'keyword' };
                    }
                }
    
                // 图片消息转人工
                if (settings.imageEnabled && message.type === MESSAGE_TYPES.IMAGE) {
                    return { shouldTransfer: true, reason: 'image' };
                }
    
                // 退款场景转人工
                if (settings.refundEnabled && message.type === MESSAGE_TYPES.REFUND) {
                    return { shouldTransfer: true, reason: 'refund' };
                }
    
                // 投诉场景转人工
                if (settings.complaintEnabled && message.content) {
                    const complaintKeywords = ['投诉', '差评', '不满意', '质量差', '态度差'];
                    if (complaintKeywords.some(keyword => message.content.includes(keyword))) {
                        return { shouldTransfer: true, reason: 'complaint' };
                    }
                }
    
                return { shouldTransfer: false, reason: 'no_match' };
            } catch (error) {
                console.error('[TransferRules] 检查转人工条件失败:', error);
                return { shouldTransfer: false, reason: 'error' };
            }
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Transfer.Rules = TransferRules;
    window.AIPDD.Transfer.State = TransferState;
    window.AIPDD.MESSAGE_TYPES = MESSAGE_TYPES;
})();