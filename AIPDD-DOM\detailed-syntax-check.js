// 详细语法检查脚本
const fs = require('fs');

function checkFile(filePath) {
    console.log(`\n检查文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ❌ 文件不存在`);
        return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    try {
        // 尝试解析JavaScript
        new Function(content);
        console.log(`  ✅ 语法正确`);
        return true;
    } catch (syntaxError) {
        console.log(`  ❌ 语法错误: ${syntaxError.message}`);
        
        // 尝试找到错误位置
        const lines = content.split('\n');
        const errorLine = syntaxError.message.match(/line (\d+)/);
        if (errorLine) {
            const lineNum = parseInt(errorLine[1]);
            console.log(`  错误行 ${lineNum}: ${lines[lineNum - 1]}`);
        }
        
        // 检查可能的问题
        console.log('\n  可能的问题:');
        
        // 检查可选链操作符
        if (content.includes('?.')) {
            console.log('  - 包含可选链操作符 (?.)，可能不被支持');
        }
        
        // 检查空值合并操作符
        if (content.includes('??')) {
            console.log('  - 包含空值合并操作符 (??)，可能不被支持');
        }
        
        // 检查扩展运算符
        if (content.includes('...')) {
            console.log('  - 包含扩展运算符 (...)');
        }
        
        // 检查模板字符串
        if (content.includes('`')) {
            console.log('  - 包含模板字符串');
        }
        
        // 检查箭头函数
        if (content.includes('=>')) {
            console.log('  - 包含箭头函数');
        }
        
        return false;
    }
}

// 检查问题文件
const problemFiles = [
    'src/core/utils/dom-utils.js',
    'src/core/transfer/transfer-manager.js',
    'src/content/content.js'
];

problemFiles.forEach(checkFile);
