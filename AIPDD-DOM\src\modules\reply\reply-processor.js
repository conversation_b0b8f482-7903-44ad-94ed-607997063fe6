/**
 * 自动回复处理器
 * 负责处理AI自动回复相关的所有功能
 */
(function(global) {
    'use strict';

    // 导入依赖
    const { debug } = global.AIPDD.Core.Utils.Debug;
    const { EventBus } = global.AIPDD.Core.Utils.EventBus;
    const { StorageManager } = global.AIPDD.Core.Storage.StorageManager;

    // 确保模块命名空间存在
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Modules) global.AIPDD.Modules = {};
    if (!global.AIPDD.Modules.Reply) global.AIPDD.Modules.Reply = {};

    /**
     * 自动回复处理器类
     */
    class ReplyProcessor {
        constructor() {
            this.storage = StorageManager.getInstance();
            this.eventBus = EventBus.getInstance();
            this.repliedMessages = {};
            this.apiUrl = "https://dify.aihaopengyou.com/api/chat-messages";
            this.autoReplyEnabled = false;
            this.messageQueue = [];
            
            // 初始化存储
            this._initStorage();
            
            // 注册事件监听
            this._registerEvents();
            
            debug('[ReplyProcessor] 回复处理器已初始化');
        }
        
        /**
         * 获取单例实例
         * @returns {ReplyProcessor} 回复处理器实例
         */
        static getInstance() {
            if (!ReplyProcessor._instance) {
                ReplyProcessor._instance = new ReplyProcessor();
            }
            return ReplyProcessor._instance;
        }
        
        /**
         * 初始化存储
         * @private
         */
        async _initStorage() {
            try {
                // 检查并初始化已回复消息存储
                await this.initRepliedMessagesStorage();
                
                // 加载自动回复设置
                const settings = await this.storage.get('autoReplySettings', {
                    enabled: false,
                    apiKey: '',
                    difyEnabled: true,
                    difyMediaEnabled: true
                });
                
                this.autoReplyEnabled = settings.enabled;
                this.apiKey = settings.apiKey;
                this.difyEnabled = settings.difyEnabled;
                this.difyMediaEnabled = settings.difyMediaEnabled;
                
                debug('[ReplyProcessor] 已加载自动回复设置:', 
                    {enabled: this.autoReplyEnabled, difyEnabled: this.difyEnabled});
            } catch (error) {
                debug('[ReplyProcessor] 初始化存储出错:', error);
            }
        }
        
        /**
         * 注册事件监听
         * @private
         */
        _registerEvents() {
            try {
                // 监听自动回复开关变更事件
                this.eventBus.on('autoReply:toggle', (enabled) => {
                    this.autoReplyEnabled = enabled;
                    debug(`[ReplyProcessor] 自动回复已${enabled ? '启用' : '禁用'}`);
                });
                
                // 监听设置变更事件
                this.eventBus.on('settings:updated', async () => {
                    await this._initStorage();
                });
            } catch (error) {
                debug('[ReplyProcessor] 注册事件监听出错:', error);
            }
        }
        
        /**
         * 处理回复请求 - 兼容content.js中的difyReplyProcess函数
         * @param {object} r 回复请求
         * @param {object} s 会话状态
         * @param {object} m 消息对象
         * @returns {Promise<any>} 处理结果
         */
        async processReply(r, s, m) {
            try {
                // 检查是否是后台商品卡请求的响应
                if (r.data && r.data._isBackgroundProductCard === true) {
                    debug('[ReplyProcessor] 检测到后台商品卡响应，跳过处理');
                    return;
                }
                
                // 优先检查转人工
                const settings = await this.storage.get('transferSettings', {
                    manualEnabled: true,  // 总开关
                    difyTransferEnabled: true // 关键词触发
                });

                // 插入dify引导转人工逻辑
                if (r.data.answer.includes("转人工") || r.data.answer.includes("转售前") || 
                    r.data.answer.includes("转售后") || r.data.answer.includes("转接")) {
                    if (settings.manualEnabled && settings.difyTransferEnabled) {
                        await this._sendReply('收到，请您稍候一下···');
                        return 'transfer';
                    } else {
                        // 获取自定义的转人工失败提示文案
                        let transferFailMessage = await this.storage.get('transferFailMessage', 
                            '实在抱歉无法转接，请稍候再联系');
                        
                        await this._sendReply(transferFailMessage + ' ' + this._getEmojiByType('apology'));
                        return;
                    }
                }

                try {
                    // 导入Dify响应处理程序
                    const difyResponseHandlerModule = await import('../../utils/handleDifyResponse.js');
                    const difyResponseHandler = difyResponseHandlerModule.createDifyResponseHandler();
                    
                    // 处理 Dify 回复(触发对应操作)
                    let answer = await difyResponseHandler.handle(r.data.answer);
                    debug('[ReplyProcessor] 触发完成', answer);
                    
                    if (answer.status === 'success') {
                        // 成功触发
                        if (answer.message) {
                            // 发完图片后，发送文本
                            debug('[ReplyProcessor] 发完图片后，发送文本', answer.message);
                            await this._sendReply(answer.message);
                        }
                        return;
                    } else if (answer.status === 'error') {
                        // 触发发生异常
                        debug('[ReplyProcessor] 触发发生异常', answer.message);
                        return;
                    } else {
                        // 正常回复
                        // 添加备注检查及处理
                        if (r.data.answer.includes('备注：')) {
                            const enabled = await this._checkBackupKeywordEnabled();
                            if (enabled) {
                                const parts = r.data.answer.split('备注：');
                                let remark = parts[1]?.trim();
                                if (remark) {
                                    // 检查是否包含✓符号，如果包含则跳过处理
                                    if (remark.includes('✓')) {
                                        debug('[ReplyProcessor] 备注已处理过，跳过添加');
                                        // 发送默认回复文案
                                        await this._sendReply('好的，已经备注了 ' + this._getEmojiByType('positive'));
                                        r.data.answer = ''; // 清空原回答，避免重复发送
                                    } else {
                                        let order_item = await this._findBackupKeywordOrderItem();
                                        if (order_item) {
                                            await this._actionBackupKeyword(remark, order_item);
                                            r.data.answer = parts[0]?.trim() ?? '谢谢';
                                        }
                                    }
                                }
                            } else {
                                // 未开启选项
                                debug('[ReplyProcessor] 添加用户备注功能被关闭，使用用户设置的回复文案');
                                // 获取用户设置的文案
                                const replySettings = await this.storage.get('orderNoteReplyMessage', 
                                    '收到，稍后为您处理');
                                
                                // 添加随机ORDER表情
                                await this._sendReply(replySettings + ' ' + this._getEmojiByType('order'));
                                return;
                            }
                        } else if (r.data.answer.includes('打标：')) {
                            // 检查回复是否包含"打标："关键词
                            debug('[ReplyProcessor] 检测到打标指令', r.data.answer);
                            
                            const enabled = await this._checkStarFlagEnabled();
                            if (enabled) {
                                // 打星标功能开启，执行打星标
                                this._actionStarFlag();
                                const parts = r.data.answer.split('打标：');
                                debug('[ReplyProcessor] 打标回复', parts);
                                // 只保留消息部分，去掉指令
                                let remark = parts[0]?.trim();
                                // 如果指令后还有文本，也合并进来
                                if (parts[1]) {
                                    remark = remark ? remark + " " + parts[1]?.trim() : parts[1]?.trim();
                                }
                                
                                if (remark) {
                                    r.data.answer = remark;
                                } else {
                                    r.data.answer = '谢谢';
                                }
                            } else {
                                // 星标功能关闭，直接发送失败提示语
                                debug('[ReplyProcessor] 添加用户星标功能被关闭，发送星标失败提示语');
                                
                                // 获取用户设置的失败提示文案
                                const replySettings = await this.storage.get('starFlagReplyMessage', 
                                    '抱歉，星标功能暂时不可用');
                                
                                // 添加歉意表情
                                await this._sendReply(replySettings + ' ' + this._getEmojiByType('apology'));
                                return;
                            }
                        }

                        // 发送最终回复
                        debug('[ReplyProcessor] 发送最终回复', r.data.answer);
                        const success = await this._sendReply(r.data.answer);
                        
                        if (success) {
                            try {
                                // 标记消息为已回复
                                if (m.unrepliedMessages && m.unrepliedMessages.length > 0) {
                                    // 标记所有未回复的消息
                                    debug(`[ReplyProcessor] 标记 ${m.unrepliedMessages.length} 条未回复消息为已回复`);
                                    for (const msgElement of m.unrepliedMessages) {
                                        await this._saveRepliedMessage(s.getPddConversationId(), 
                                            msgElement.id || (Date.now() + '_' + Math.random().toString(36).substring(2, 8)));
                                        
                                        // 标记已处理
                                        msgElement._markedAsReplied = true;
                                        
                                        // 添加视觉标记
                                        await this._addVisualReplyMark(msgElement);
                                    }
                                } else if (m.element) {
                                    // 兼容旧逻辑，只标记当前消息
                                    await this._saveRepliedMessage(s.getPddConversationId(), 
                                        m.id || (Date.now() + '_' + Math.random().toString(36).substring(2, 8)));
                                    
                                    // 标记已处理
                                    m.element._markedAsReplied = true;
                                    
                                    // 添加视觉标记
                                    await this._addVisualReplyMark(m.element);
                                }
                            } catch (markError) {
                                debug('[ReplyProcessor] 标记消息为已回复出错:', markError);
                            }
                            
                            if (s) {
                                s.processedMessages.add(m.id);
                                s.lastProcessedTime = Date.now();
                            }
                            
                            // 更新浮动球状态
                            if (global.floatingBallInstance) {
                                global.floatingBallInstance.setStatus('success');
                            }
                        } else {
                            if (global.floatingBallInstance) {
                                global.floatingBallInstance.setStatus('error');
                            }
                        }
                    }
                } catch (error) {
                    debug('[ReplyProcessor] AI API回复处理错误:', error);
                    if (global.floatingBallInstance) {
                        global.floatingBallInstance.setStatus('error');
                    }
                }
                
                // 返回标记
                return { messageMarkedAsReplied: true };
            } catch (error) {
                debug('[ReplyProcessor] 处理回复出错:', error);
                return false;
            }
        }
        
        /**
         * 发送文本消息请求到Dify
         * @param {string} content 消息内容
         * @param {object} userInfo 用户信息
         * @param {string} conversationId 会话ID
         * @param {object} state 会话状态
         * @param {object} message 原始消息对象
         * @returns {Promise<boolean>} 是否成功
         */
        async sendDifyTxtRequest(content, userInfo, conversationId, state, message) {
            try {
                debug('[ReplyProcessor] 发送文本请求到Dify:', {content: content.substring(0, 50) + '...'});
                
                const requestBody = {
                    inputs: {
                        query: content,
                        customer_name: userInfo.nickname || "顾客",
                        conversation_id: conversationId,
                        product_name: state.productName || "",
                        message_type: "text"
                    },
                    response_mode: "blocking",
                    user: userInfo.user_id || conversationId
                };
                
                const response = await this._sendDifyRequest(requestBody);
                return response;
            } catch (error) {
                debug('[ReplyProcessor] 发送文本请求到Dify出错:', error);
                return null;
            }
        }
        
        /**
         * 发送商品请求到Dify
         * @param {object} content 商品信息
         * @param {object} userInfo 用户信息
         * @param {string} conversationId 会话ID
         * @param {object} state 会话状态
         * @param {object} message 原始消息对象
         * @returns {Promise<boolean>} 是否成功
         */
        async sendDifyProductRequest(content, userInfo, conversationId, state, message) {
            try {
                debug('[ReplyProcessor] 发送商品请求到Dify');
                
                const productInfo = message.productCard || {};
                const requestBody = {
                    inputs: {
                        query: message.content || "",
                        customer_name: userInfo.nickname || "顾客",
                        conversation_id: conversationId,
                        product_name: productInfo.title || state.productName || "",
                        product_price: productInfo.price || "",
                        product_url: productInfo.url || "",
                        message_type: "product"
                    },
                    response_mode: "blocking",
                    user: userInfo.user_id || conversationId
                };
                
                const response = await this._sendDifyRequest(requestBody);
                return response;
            } catch (error) {
                debug('[ReplyProcessor] 发送商品请求到Dify出错:', error);
                return null;
            }
        }
        
        /**
         * 发送媒体消息请求到Dify
         * @param {string} emoji_content 表情内容
         * @param {object} userInfo 用户信息
         * @returns {Promise<boolean>} 是否成功
         */
        async sendDifyMediaRequest(emoji_content, userInfo) {
            try {
                const enabled = await this._checkDifyMediaEnabled();
                if (!enabled) {
                    debug('[ReplyProcessor] 媒体消息回复未启用');
                    return null;
                }
                
                debug('[ReplyProcessor] 发送媒体请求到Dify:', emoji_content);
                
                const requestBody = {
                    inputs: {
                        query: emoji_content,
                        customer_name: userInfo.nickname || "顾客",
                        message_type: "emoji"
                    },
                    response_mode: "blocking",
                    user: userInfo.user_id
                };
                
                const response = await this._sendDifyRequest(requestBody);
                return response;
            } catch (error) {
                debug('[ReplyProcessor] 发送媒体请求到Dify出错:', error);
                return null;
            }
        }
        
        /**
         * 发送图片请求到Dify
         * @param {string} imageUrl 图片URL
         * @param {object} userInfo 用户信息
         * @returns {Promise<boolean>} 是否成功
         */
        async sendDifyImageRequest(imageUrl, userInfo) {
            try {
                const enabled = await this._checkDifyMediaEnabled();
                if (!enabled) {
                    debug('[ReplyProcessor] 图片消息回复未启用');
                    return null;
                }
                
                debug('[ReplyProcessor] 发送图片请求到Dify');
                
                const requestBody = {
                    inputs: {
                        image_url: imageUrl,
                        customer_name: userInfo.nickname || "顾客",
                        message_type: "image"
                    },
                    response_mode: "blocking",
                    user: userInfo.user_id
                };
                
                const response = await this._sendDifyRequest(requestBody);
                return response;
            } catch (error) {
                debug('[ReplyProcessor] 发送图片请求到Dify出错:', error);
                return null;
            }
        }
        
        /**
         * 发送请求到Dify API
         * @param {object} requestBody 请求体
         * @returns {Promise<object>} 响应数据
         * @private
         */
        async _sendDifyRequest(requestBody) {
            try {
                const apiKey = await this.storage.get('difyApiKey', '');
                if (!apiKey) {
                    throw new Error('API密钥未设置');
                }
                
                const response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                debug('[ReplyProcessor] API请求出错:', error);
                throw error;
            }
        }
        
        /**
         * 发送回复消息
         * @param {string} message 回复内容
         * @returns {Promise<boolean>} 是否成功
         * @private
         */
        async _sendReply(message) {
            // 使用全局sendMessage函数
            if (typeof global.sendMessage === 'function') {
                return await global.sendMessage(message);
            } else {
                debug('[ReplyProcessor] 全局sendMessage函数不可用');
                return false;
            }
        }
        
        /**
         * 添加视觉回复标记
         * @param {HTMLElement} messageElement 消息元素
         * @returns {Promise<void>}
         * @private
         */
        async _addVisualReplyMark(messageElement) {
            // 使用全局addVisualReplyMark函数
            if (typeof global.addVisualReplyMark === 'function') {
                return await global.addVisualReplyMark(messageElement);
            } else {
                debug('[ReplyProcessor] 全局addVisualReplyMark函数不可用');
                
                if (!messageElement) return;
                
                try {
                    // 检查是否已有标记
                    if (messageElement.querySelector('.aipdd-replied-mark')) {
                        return;
                    }
                    
                    // 创建标记元素
                    const mark = document.createElement('div');
                    mark.className = 'aipdd-replied-mark';
                    mark.style.cssText = `
                        position: absolute;
                        right: 10px;
                        top: 5px;
                        background-color: rgba(76, 175, 80, 0.7);
                        color: white;
                        padding: 2px 5px;
                        border-radius: 3px;
                        font-size: 12px;
                        z-index: 10;
                    `;
                    mark.textContent = '已回复';
                    
                    // 添加到消息元素
                    if (messageElement.style.position !== 'relative') {
                        messageElement.style.position = 'relative';
                    }
                    
                    messageElement.appendChild(mark);
                } catch (error) {
                    debug('[ReplyProcessor] 添加视觉标记出错:', error);
                }
            }
        }
        
        /**
         * 保存已回复消息
         * @param {string} conversationId 会话ID
         * @param {string} messageId 消息ID
         * @returns {Promise<void>}
         * @private
         */
        async _saveRepliedMessage(conversationId, messageId) {
            // 使用全局saveRepliedMessage函数
            if (typeof global.saveRepliedMessage === 'function') {
                return await global.saveRepliedMessage(conversationId, messageId);
            } else {
                debug('[ReplyProcessor] 全局saveRepliedMessage函数不可用');
                
                try {
                    if (!conversationId || !messageId) return;
                    
                    // 确保会话ID的数据存在
                    if (!this.repliedMessages[conversationId]) {
                        this.repliedMessages[conversationId] = {};
                    }
                    
                    // 记录消息ID和时间戳
                    this.repliedMessages[conversationId][messageId] = Date.now();
                    
                    // 保存到存储
                    await this.storage.set('repliedMessages', this.repliedMessages);
                } catch (error) {
                    debug('[ReplyProcessor] 保存已回复消息出错:', error);
                }
            }
        }
        
        /**
         * 初始化已回复消息存储
         * @returns {Promise<void>}
         * @private
         */
        async initRepliedMessagesStorage() {
            // 使用全局initRepliedMessagesStorage函数
            if (typeof global.initRepliedMessagesStorage === 'function') {
                return await global.initRepliedMessagesStorage();
            } else {
                debug('[ReplyProcessor] 全局initRepliedMessagesStorage函数不可用');
                
                try {
                    // 加载已回复消息记录
                    this.repliedMessages = await this.storage.get('repliedMessages', {});
                    
                    // 清理过期数据
                    await this._cleanupOldReplies();
                } catch (error) {
                    debug('[ReplyProcessor] 初始化已回复消息存储出错:', error);
                    this.repliedMessages = {};
                }
            }
        }
        
        /**
         * 检查消息是否已回复
         * @param {string} conversationId 会话ID
         * @param {string} messageId 消息ID
         * @returns {Promise<boolean>} 是否已回复
         * @private
         */
        async _isMessageReplied(conversationId, messageId) {
            // 使用全局isMessageReplied函数
            if (typeof global.isMessageReplied === 'function') {
                return await global.isMessageReplied(conversationId, messageId);
            } else {
                debug('[ReplyProcessor] 全局isMessageReplied函数不可用');
                
                try {
                    return !!(this.repliedMessages[conversationId] && 
                              this.repliedMessages[conversationId][messageId]);
                } catch (error) {
                    debug('[ReplyProcessor] 检查消息是否已回复出错:', error);
                    return false;
                }
            }
        }
        
        /**
         * 清理过期回复数据
         * @returns {Promise<void>}
         * @private
         */
        async _cleanupOldReplies() {
            // 使用全局cleanupOldReplies函数
            if (typeof global.cleanupOldReplies === 'function') {
                return await global.cleanupOldReplies();
            } else {
                debug('[ReplyProcessor] 全局cleanupOldReplies函数不可用');
                
                try {
                    const now = Date.now();
                    const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
                    let changed = false;
                    
                    // 遍历所有会话
                    for (const conversationId in this.repliedMessages) {
                        // 遍历会话中的消息
                        for (const messageId in this.repliedMessages[conversationId]) {
                            const timestamp = this.repliedMessages[conversationId][messageId];
                            
                            // 删除超过一天的数据
                            if (now - timestamp > oneDay) {
                                delete this.repliedMessages[conversationId][messageId];
                                changed = true;
                            }
                        }
                        
                        // 如果会话没有消息，删除会话
                        if (Object.keys(this.repliedMessages[conversationId]).length === 0) {
                            delete this.repliedMessages[conversationId];
                            changed = true;
                        }
                    }
                    
                    // 如果有变更，保存数据
                    if (changed) {
                        await this.storage.set('repliedMessages', this.repliedMessages);
                    }
                } catch (error) {
                    debug('[ReplyProcessor] 清理过期回复数据出错:', error);
                }
            }
        }
        
        /**
         * 获取表情
         * @param {string} type 表情类型
         * @returns {string} 表情
         * @private
         */
        _getEmojiByType(type) {
            // 使用全局getEmojiByType函数
            if (typeof global.getEmojiByType === 'function') {
                return global.getEmojiByType(type);
            } else {
                const emojis = {
                    'positive': ['😊', '👍', '🌟', '💯', '🎉'],
                    'negative': ['😔', '😕', '🙁', '😢', '😞'],
                    'neutral': ['🤔', '😐', '🧐', '💭', '🔍'],
                    'apology': ['🙏', '😓', '🥺', '💔', '😔'],
                    'order': ['📦', '🛒', '🛍️', '📋', '🧾'],
                    'question': ['❓', '🤔', '🧐', '❔', '🔍'],
                    'wait': ['⏳', '⌛', '🕒', '🕙', '⏰'],
                    'delivery': ['🚚', '🛵', '🚗', '✈️', '📦']
                };
                
                const typeEmojis = emojis[type] || emojis['neutral'];
                const randomIndex = Math.floor(Math.random() * typeEmojis.length);
                return typeEmojis[randomIndex];
            }
        }
        
        /**
         * 检查是否启用了备注关键词功能
         * @returns {Promise<boolean>} 是否启用
         * @private
         */
        async _checkBackupKeywordEnabled() {
            // 使用全局checkBackupKeywordEnabled函数
            if (typeof global.checkBackupKeywordEnabled === 'function') {
                return await global.checkBackupKeywordEnabled();
            } else {
                try {
                    const settings = await this.storage.get('backupKeywordSettings', {enabled: false});
                    return settings.enabled === true;
                } catch (error) {
                    debug('[ReplyProcessor] 检查备注关键词功能是否启用出错:', error);
                    return false;
                }
            }
        }
        
        /**
         * 检查是否启用了星标功能
         * @returns {Promise<boolean>} 是否启用
         * @private
         */
        async _checkStarFlagEnabled() {
            // 使用全局checkStarFlagEnabled函数
            if (typeof global.checkStarFlagEnabled === 'function') {
                return await global.checkStarFlagEnabled();
            } else {
                try {
                    const settings = await this.storage.get('starFlagSettings', {enabled: false});
                    return settings.enabled === true;
                } catch (error) {
                    debug('[ReplyProcessor] 检查星标功能是否启用出错:', error);
                    return false;
                }
            }
        }
        
        /**
         * 检查是否启用了媒体消息回复
         * @returns {Promise<boolean>} 是否启用
         * @private
         */
        async _checkDifyMediaEnabled() {
            // 使用全局checkDifyMediaEnabled函数
            if (typeof global.checkDifyMediaEnabled === 'function') {
                return await global.checkDifyMediaEnabled();
            } else {
                try {
                    const settings = await this.storage.get('autoReplySettings', {});
                    return settings.difyMediaEnabled === true;
                } catch (error) {
                    debug('[ReplyProcessor] 检查媒体消息回复是否启用出错:', error);
                    return false;
                }
            }
        }
        
        /**
         * 查找备注关键词订单项
         * @returns {Promise<HTMLElement>} 订单项元素
         * @private
         */
        async _findBackupKeywordOrderItem() {
            // 使用全局findBackupKeywordOrderItem函数
            if (typeof global.findBackupKeywordOrderItem === 'function') {
                return await global.findBackupKeywordOrderItem();
            } else {
                debug('[ReplyProcessor] 全局findBackupKeywordOrderItem函数不可用');
                return null;
            }
        }
        
        /**
         * 执行备注关键词动作
         * @param {string} content 备注内容
         * @param {HTMLElement} order_item 订单项元素
         * @returns {Promise<void>}
         * @private
         */
        async _actionBackupKeyword(content, order_item) {
            // 使用全局actionBackupKeyword函数
            if (typeof global.actionBackupKeyword === 'function') {
                return await global.actionBackupKeyword(content, order_item);
            } else {
                debug('[ReplyProcessor] 全局actionBackupKeyword函数不可用');
                return null;
            }
        }
        
        /**
         * 执行星标动作
         * @returns {Promise<void>}
         * @private
         */
        async _actionStarFlag() {
            // 使用全局actionStarFlag函数
            if (typeof global.actionStarFlag === 'function') {
                return await global.actionStarFlag();
            } else {
                debug('[ReplyProcessor] 全局actionStarFlag函数不可用');
                return null;
            }
        }
    }

    // 创建单例实例
    const instance = new ReplyProcessor();
    
    // 导出到命名空间
    global.AIPDD.Modules.Reply.ReplyProcessor = instance;

})(window);