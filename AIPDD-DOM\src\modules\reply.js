// 调试日志
function debug(...args) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}]`, ...args);
}

/**
 * 拼多多聊天回复管理类
 */
class Reply {
    constructor() {
        this.isRunning = false;
        this.countdownValue = 0;  // 改为2秒，方便测试
        this.setupEnterToSend();
    }

    /**
     * 查找发送按钮和输入框
     * @returns {Object} 包含button和input元素的对象
     */
    findElements() {
        const input = document.getElementById('replyTextarea');
        const button = document.querySelector('div.send-btn');
        debug('查找结果:', {
            button: button ? '找到按钮' : '未找到按钮',
            input: input ? '找到输入框' : '未找到输入框'
        });
        return { button, input };
    }

    /**
     * 设置回车发送功能
     */
    setupEnterToSend() {
        debug('已设置回车发送功能');
        document.addEventListener('keydown', async (event) => {
            if (event.key === 'Enter' && !event.shiftKey && !this.isRunning) {
                const elements = this.findElements();
                if (!elements.input || !elements.button) return;
                
                const message = elements.input.value.trim();
                if (!message) return;
                
                event.preventDefault();
                debug('回车事件未触发发送，尝试点击发送按钮');
                if (!this.isRunning) {
                    await this.start(message);
                } else {
                    debug('发送流程已在运行或消息为空');
                }
            }
        });
    }

    /**
     * 发送消息
     * @param {HTMLElement} input 输入框元素
     * @param {string} message 要发送的消息
     * @returns {Promise<boolean>} 是否发送成功
     */
    async sendMessage(input, message) {
        try {
            // 1. 设置输入框内容和焦点
            input.focus();
            input.value = message;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            
            // 2. 模拟Enter键事件
            const enterKeyEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
            });
            input.dispatchEvent(enterKeyEvent);
            
            // 3. 检查发送结果
            return await this.checkMessageSent(input, message);
        } catch (error) {
            debug('发送消息失败:', error);
            return false;
        }
    }

    /**
     * 检查消息是否发送成功
     * @param {HTMLElement} input 输入框元素
     * @param {string} originalMessage 原始消息
     * @returns {Promise<boolean>} 是否发送成功
     */
    async checkMessageSent(input, originalMessage) {
        debug('等待消息发送结果...');
        for (let i = 0; i < 3; i++) {
            const currentInput = input.value.trim();
            debug('输入框当前内容:', currentInput);

            if (currentInput === '') {
                debug('输入框已清空，消息发送成功！');
                return true;
            }

            if (currentInput !== originalMessage) {
                debug('输入框内容已改变，消息可能已发送');
                return true;
            }

            debug(`第${i + 1}次检查失败，等待后重试...`);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 发送消息时不需要再次设置输入框内容
            const enterKeyEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
            });
            input.dispatchEvent(enterKeyEvent);

            //检查是否有弹窗repeat-interceptor-popup .el-button el-button--default el-button--mini
            let popup = document.querySelector('.repeat-interceptor-popup .el-button--default.el-button--mini');
            if(popup) {
                debug('有重复弹窗，点击');
                popup.click();
            }
        }

        debug('消息发送失败：未能确认消息发送成功');
        return false;
    }

    /**
     * 添加按钮动画效果
     * @param {HTMLElement} button 按钮元素
     * @returns {Object} 包含原始样式和动画样式表的对象
     */
    addButtonAnimation(button) {
        const originalStyle = button.style.cssText;
        const originalText = button.textContent;

        // 创建动画样式表
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            @keyframes pddButtonPulse {
                0% { box-shadow: 0 0 5px rgba(255, 0, 0, 0.5); }
                50% { box-shadow: 0 0 15px rgba(255, 0, 0, 0.8); }
                100% { box-shadow: 0 0 5px rgba(255, 0, 0, 0.5); }
            }
        `;
        document.head.appendChild(styleSheet);

        // 添加动画样式
        button.style.cssText = `
            ${originalStyle}
            border: 2px solid rgba(255, 0, 0, 0.5) !important;
            box-shadow: 0 0 5px rgba(255, 0, 0, 0.5) !important;
            transition: all 0.5s ease !important;
            animation: pddButtonPulse 1s infinite !important;
        `;

        return { originalStyle, originalText, styleSheet };
    }

    /**
     * 移除按钮动画效果
     * @param {HTMLElement} button 按钮元素
     * @param {Object} styles 原始样式对象
     */
    removeButtonAnimation(button, styles) {
        if (styles.styleSheet && styles.styleSheet.parentNode) {
            styles.styleSheet.parentNode.removeChild(styles.styleSheet);
        }
        button.style.cssText = styles.originalStyle;
        button.textContent = styles.originalText;
    }

    /**
     * 添加输入框动画效果
     * @param {HTMLElement} input 输入框元素
     * @returns {Object} 包含原始样式的对象
     */
    addInputAnimation(input) {
        const originalStyle = input.style.cssText;
        
        // 复用已有的 pddButtonPulse 动画
        input.style.cssText = `
            ${originalStyle}
            border: 2px solid rgba(255, 0, 0, 0.5) !important;
            box-shadow: 0 0 5px rgba(255, 0, 0, 0.5) !important;
            transition: all 0.5s ease !important;
            animation: pddButtonPulse 1s !important;
        `;

        return { originalStyle };
    }

    /**
     * 移除输入框动画效果
     * @param {HTMLElement} input 输入框元素
     * @param {Object} styles 原始样式对象
     */
    removeInputAnimation(input, styles) {
        input.style.cssText = styles.originalStyle;
    }

    /**
     * 开始发送消息流程
     * @param {string} message 要发送的消息
     * @returns {Promise<boolean>} 是否发送成功
     */
    async start(message, sendTime = 0) {
        if (this.isRunning || !message) {
            debug('发送流程已在运行或消息为空');
            return false;
        }

        this.isRunning = true;
        debug('开始running消息');
        try {
            const elements = this.findElements();
            if (!elements.input || !elements.button) {
                debug('未找到必要的页面元素');
                return false;
            }

            // 先设置输入框内容
            elements.input.focus();
            elements.input.value = message;
            elements.input.dispatchEvent(new Event('input', { bubbles: true }));

            // 添加输入框动画
            const inputStyles = this.addInputAnimation(elements.input);
            setTimeout(() => {
                this.removeInputAnimation(elements.input, inputStyles);
            }, 100);

            // 添加按钮动画
            const styles = this.addButtonAnimation(elements.button);

            try {
                // 开始倒计时
                let secondsLeft = this.countdownValue;
                if(sendTime > 0) {
                    secondsLeft = sendTime;
                }
                debug('倒计时:', secondsLeft + '秒');
                
                while (secondsLeft > 0) {
                    elements.button.textContent = `发送(${secondsLeft}s)`;
                    debug('倒计时:', secondsLeft + '秒');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    if(sendTime > 0) {
                        if(elements.input.value !== message) {
                            debug('人工改变输入框内容，取消倒计时...');
                            return false;
                        }
                    }
                    secondsLeft--;
                }

                elements.button.textContent = '发送中...';
                debug('倒计时结束，准备发送...');

                //检查是否有弹窗repeat-interceptor-popup .el-button el-button--default el-button--mini
                let popup = document.querySelector('.repeat-interceptor-popup .el-button--default.el-button--mini');
                if(popup) {
                    popup.click();
                }

                // 发送消息时不需要再次设置输入框内容
                const enterKeyEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                elements.input.dispatchEvent(enterKeyEvent);



                return await this.checkMessageSent(elements.input, message);
            } finally {
                // 清理动画效果
                this.removeButtonAnimation(elements.button, styles);
            }
        } finally {
            debug('发送流程结束');
            this.isRunning = false;
        }
    }
}

// 导出类
window.Reply = Reply;
