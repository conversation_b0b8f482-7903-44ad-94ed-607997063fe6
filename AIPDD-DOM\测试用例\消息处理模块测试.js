/**
 * 消息处理模块测试用例
 * 测试消息解析、类型判断和处理功能
 */

// 测试配置
const TEST_CONFIG = {
  // 测试消息
  textMessage: {
    id: 'msg_text_001',
    element: '<div class="message">这是一条文本消息</div>',
    content: '这是一条文本消息',
    type: 'text'
  },
  
  productMessage: {
    id: 'msg_product_001',
    element: '<div class="message product"><div class="product-info">测试商品</div></div>',
    content: '测试商品',
    type: 'product'
  },
  
  orderMessage: {
    id: 'msg_order_001',
    element: '<div class="message order"><div class="order-info">订单号: 123456</div></div>',
    content: '订单号: 123456',
    type: 'order'
  },
  
  // 测试会话ID
  conversationId: 'test_conversation_001',
  
  // 测试延迟
  delay: 100
};

/**
 * 延迟执行函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise<void>}
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 创建DOM元素
 * @param {string} html - HTML字符串
 * @returns {Element} 创建的DOM元素
 */
function createElementFromHTML(html) {
  const div = document.createElement('div');
  div.innerHTML = html.trim();
  return div.firstChild;
}

/**
 * 测试消息解析
 */
async function testMessageParsing() {
  console.log('开始测试消息解析...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建测试消息元素
    const textElement = createElementFromHTML(TEST_CONFIG.textMessage.element);
    const productElement = createElementFromHTML(TEST_CONFIG.productMessage.element);
    const orderElement = createElementFromHTML(TEST_CONFIG.orderMessage.element);
    
    // 解析文本消息
    const parsedTextMessage = await Message.Parser.parse(textElement, TEST_CONFIG.conversationId);
    console.log('解析的文本消息:', parsedTextMessage);
    
    if (parsedTextMessage && parsedTextMessage.content === TEST_CONFIG.textMessage.content) {
      console.log('✓ 文本消息解析成功');
    } else {
      throw new Error('文本消息解析失败');
    }
    
    // 解析商品消息
    const parsedProductMessage = await Message.Parser.parse(productElement, TEST_CONFIG.conversationId);
    console.log('解析的商品消息:', parsedProductMessage);
    
    if (parsedProductMessage && parsedProductMessage.content === TEST_CONFIG.productMessage.content) {
      console.log('✓ 商品消息解析成功');
    } else {
      throw new Error('商品消息解析失败');
    }
    
    // 解析订单消息
    const parsedOrderMessage = await Message.Parser.parse(orderElement, TEST_CONFIG.conversationId);
    console.log('解析的订单消息:', parsedOrderMessage);
    
    if (parsedOrderMessage && parsedOrderMessage.content === TEST_CONFIG.orderMessage.content) {
      console.log('✓ 订单消息解析成功');
    } else {
      throw new Error('订单消息解析失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息解析测试失败:', error);
    return false;
  }
}

/**
 * 测试消息类型判断
 */
async function testMessageTypeDetection() {
  console.log('开始测试消息类型判断...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建测试消息
    const textMessage = {
      content: '这是一条文本消息',
      conversationId: TEST_CONFIG.conversationId
    };
    
    const productMessage = {
      content: '测试商品',
      conversationId: TEST_CONFIG.conversationId,
      productInfo: {
        name: '测试商品',
        price: '99.00'
      }
    };
    
    const orderMessage = {
      content: '订单号: 123456',
      conversationId: TEST_CONFIG.conversationId,
      orderInfo: {
        orderNumber: '123456'
      }
    };
    
    // 判断文本消息类型
    const textType = Message.Parser.determineType(textMessage);
    console.log('文本消息类型:', textType);
    
    if (textType === 'text') {
      console.log('✓ 文本消息类型判断成功');
    } else {
      throw new Error('文本消息类型判断失败');
    }
    
    // 判断商品消息类型
    const productType = Message.Parser.determineType(productMessage);
    console.log('商品消息类型:', productType);
    
    if (productType === 'product') {
      console.log('✓ 商品消息类型判断成功');
    } else {
      throw new Error('商品消息类型判断失败');
    }
    
    // 判断订单消息类型
    const orderType = Message.Parser.determineType(orderMessage);
    console.log('订单消息类型:', orderType);
    
    if (orderType === 'order') {
      console.log('✓ 订单消息类型判断成功');
    } else {
      throw new Error('订单消息类型判断失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息类型判断测试失败:', error);
    return false;
  }
}

/**
 * 测试消息处理
 */
async function testMessageProcessing() {
  console.log('开始测试消息处理...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建测试消息
    const testMessage = {
      id: 'test_msg_' + Date.now(),
      content: '测试消息内容',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now()
    };
    
    // 创建处理结果Promise
    const processingPromise = new Promise((resolve) => {
      // 添加临时处理器
      const handler = (message) => {
        console.log('处理器收到消息:', message);
        
        // 验证消息内容
        if (message.id === testMessage.id) {
          resolve(true);
        } else {
          resolve(false);
        }
        
        // 移除处理器
        Message.Handler.removeHandler(handler);
      };
      
      // 添加处理器
      Message.Handler.addHandler(handler);
    });
    
    // 设置超时
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => resolve(false), 1000);
    });
    
    // 处理消息
    await Message.process(testMessage);
    
    // 等待处理结果或超时
    const processed = await Promise.race([processingPromise, timeoutPromise]);
    
    if (processed) {
      console.log('✓ 消息处理测试成功');
    } else {
      throw new Error('消息处理测试失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息处理测试失败:', error);
    return false;
  }
}

/**
 * 测试消息过滤
 */
async function testMessageFiltering() {
  console.log('开始测试消息过滤...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建测试消息
    const normalMessage = {
      id: 'normal_msg_' + Date.now(),
      content: '正常消息',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now()
    };
    
    const spamMessage = {
      id: 'spam_msg_' + Date.now(),
      content: '广告消息',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now(),
      isSpam: true
    };
    
    // 添加过滤器
    const originalFilters = Message.Parser.filters || [];
    Message.Parser.filters = [
      ...originalFilters,
      (message) => !message.isSpam
    ];
    
    // 创建处理结果Promise
    let normalProcessed = false;
    let spamProcessed = false;
    
    const handler = (message) => {
      console.log('处理器收到消息:', message);
      
      if (message.id === normalMessage.id) {
        normalProcessed = true;
      } else if (message.id === spamMessage.id) {
        spamProcessed = true;
      }
    };
    
    // 添加处理器
    Message.Handler.addHandler(handler);
    
    // 处理消息
    await Message.process(normalMessage);
    await Message.process(spamMessage);
    
    // 等待处理完成
    await delay(TEST_CONFIG.delay);
    
    // 移除处理器
    Message.Handler.removeHandler(handler);
    
    // 恢复原始过滤器
    Message.Parser.filters = originalFilters;
    
    // 验证过滤结果
    if (normalProcessed && !spamProcessed) {
      console.log('✓ 消息过滤测试成功');
    } else {
      throw new Error(`消息过滤测试失败: 正常消息=${normalProcessed}, 垃圾消息=${spamProcessed}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息过滤测试失败:', error);
    return false;
  }
}

/**
 * 测试消息已回复标记
 */
async function testMessageReplyMarking() {
  console.log('开始测试消息已回复标记...');
  
  try {
    const Message = window.AIPDD.Message;
    const Storage = window.AIPDD.Storage;
    
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    if (!Storage) {
      console.log('⚠️ 存储模块未加载，跳过已回复标记测试');
      return true;
    }
    
    // 创建测试消息
    const testMessage = {
      id: 'reply_test_' + Date.now(),
      content: '测试回复标记',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now()
    };
    
    // 确保消息未被标记为已回复
    const initiallyMarked = await Message.isReplied(testMessage);
    
    if (initiallyMarked) {
      throw new Error('消息初始状态错误，已被标记为已回复');
    }
    
    // 标记消息为已回复
    await Message.markReplied(testMessage);
    
    // 检查消息是否被标记为已回复
    const markedAsReplied = await Message.isReplied(testMessage);
    
    if (markedAsReplied) {
      console.log('✓ 消息已回复标记测试成功');
    } else {
      throw new Error('消息未被正确标记为已回复');
    }
    
    // 清理测试数据
    await Storage.remove(`replied_${TEST_CONFIG.conversationId}_${testMessage.id}`);
    
    return true;
  } catch (error) {
    console.error('❌ 消息已回复标记测试失败:', error);
    return false;
  }
}

/**
 * 测试消息批处理
 */
async function testMessageBatchProcessing() {
  console.log('开始测试消息批处理...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建测试消息批次
    const batchSize = 5;
    const testBatch = [];
    const processedIds = new Set();
    
    for (let i = 0; i < batchSize; i++) {
      testBatch.push({
        id: `batch_msg_${i}_${Date.now()}`,
        content: `批处理测试消息 ${i}`,
        type: 'text',
        conversationId: TEST_CONFIG.conversationId,
        timestamp: Date.now() + i
      });
    }
    
    // 创建处理结果Promise
    const processingPromise = new Promise((resolve) => {
      // 添加临时处理器
      const handler = (message) => {
        console.log('批处理器收到消息:', message);
        
        // 记录处理的消息ID
        if (message.id && message.id.startsWith('batch_msg_')) {
          processedIds.add(message.id);
        }
        
        // 如果所有消息都已处理，解析Promise
        if (processedIds.size === batchSize) {
          // 移除处理器
          Message.Handler.removeHandler(handler);
          resolve(true);
        }
      };
      
      // 添加处理器
      Message.Handler.addHandler(handler);
    });
    
    // 设置超时
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => resolve(false), 2000);
    });
    
    // 批量处理消息
    await Message.processBatch(testBatch);
    
    // 等待处理结果或超时
    const processed = await Promise.race([processingPromise, timeoutPromise]);
    
    if (processed) {
      console.log('✓ 消息批处理测试成功');
    } else {
      throw new Error(`消息批处理测试失败: 处理了 ${processedIds.size}/${batchSize} 条消息`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息批处理测试失败:', error);
    return false;
  }
}

/**
 * 测试消息去重
 */
async function testMessageDeduplication() {
  console.log('开始测试消息去重...');
  
  try {
    const Message = window.AIPDD.Message;
    if (!Message) {
      throw new Error('消息处理模块未加载');
    }
    
    // 创建重复的测试消息
    const duplicateId = `duplicate_msg_${Date.now()}`;
    const duplicateMessage1 = {
      id: duplicateId,
      content: '重复消息',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now()
    };
    
    const duplicateMessage2 = {
      id: duplicateId,
      content: '重复消息',
      type: 'text',
      conversationId: TEST_CONFIG.conversationId,
      timestamp: Date.now() + 1
    };
    
    // 计数处理次数
    let processCount = 0;
    
    // 创建处理结果Promise
    const processingPromise = new Promise((resolve) => {
      // 添加临时处理器
      const handler = (message) => {
        console.log('去重处理器收到消息:', message);
        
        if (message.id === duplicateId) {
          processCount++;
        }
        
        // 延迟解析Promise，确保两条消息都有机会被处理
        setTimeout(() => {
          // 移除处理器
          Message.Handler.removeHandler(handler);
          resolve(processCount);
        }, TEST_CONFIG.delay * 2);
      };
      
      // 添加处理器
      Message.Handler.addHandler(handler);
    });
    
    // 处理重复消息
    await Message.process(duplicateMessage1);
    await delay(TEST_CONFIG.delay);
    await Message.process(duplicateMessage2);
    
    // 等待处理结果
    const processedCount = await processingPromise;
    
    // 验证去重结果
    if (processedCount === 1) {
      console.log('✓ 消息去重测试成功');
    } else {
      throw new Error(`消息去重测试失败: 重复消息被处理了 ${processedCount} 次`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 消息去重测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('===== 消息处理模块测试开始 =====');
  
  let passedCount = 0;
  let failedCount = 0;
  
  // 测试消息解析
  if (await testMessageParsing()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息类型判断
  if (await testMessageTypeDetection()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息处理
  if (await testMessageProcessing()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息过滤
  if (await testMessageFiltering()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息已回复标记
  if (await testMessageReplyMarking()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息批处理
  if (await testMessageBatchProcessing()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试消息去重
  if (await testMessageDeduplication()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n===== 消息处理模块测试结束 =====');
  console.log(`通过: ${passedCount} 测试`);
  console.log(`失败: ${failedCount} 测试`);
  
  return {
    total: passedCount + failedCount,
    passed: passedCount,
    failed: failedCount,
    success: failedCount === 0
  };
}

// 导出测试函数
window.MessageModuleTest = {
  runAllTests,
  testMessageParsing,
  testMessageTypeDetection,
  testMessageProcessing,
  testMessageFiltering,
  testMessageReplyMarking,
  testMessageBatchProcessing,
  testMessageDeduplication
}; 