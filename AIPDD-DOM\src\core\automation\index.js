/**
 * 自动化模块入口文件
 * 导出自动化管理器和相关功能
 */
(function(global) {
    'use strict';

    // 导入依赖
    const AutomationManager = global.AIPDD?.Core?.Automation?.AutomationManager;
    const TaskState = global.AIPDD?.Core?.Automation?.TaskState;
    const { debug } = global.AIPDD?.Core?.Utils?.Debug || { debug: console.log.bind(console, '[Automation]') };

    /**
     * 自动化模块
     */
    class Automation {
        /**
         * 初始化自动化模块
         * @returns {Promise<boolean>} 是否初始化成功
         */
        static async init() {
            debug('初始化自动化模块');
            
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            try {
                // 初始化自动化管理器
                const result = await AutomationManager.init();
                
                // 导出兼容API
                if (!global.toggleAutoReply) {
                    global.toggleAutoReply = AutomationManager.toggleAutoReply.bind(AutomationManager);
                }
                
                if (!global.checkShouldTransfer) {
                    global.checkShouldTransfer = AutomationManager.checkShouldTransfer.bind(AutomationManager);
                }
                
                debug('自动化模块初始化完成');
                return result;
            } catch (error) {
                debug('初始化自动化模块失败:', error);
                return false;
            }
        }
        
        /**
         * 启动自动化
         * @returns {Promise<boolean>} 是否启动成功
         */
        static async start() {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return await AutomationManager.start();
        }
        
        /**
         * 停止自动化
         * @returns {Promise<boolean>} 是否停止成功
         */
        static async stop() {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return await AutomationManager.stop();
        }
        
        /**
         * 切换自动回复状态
         * @param {boolean} enabled - 是否启用
         * @returns {Promise<boolean>} 是否成功
         */
        static async toggleAutoReply(enabled = null) {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return await AutomationManager.toggleAutoReply(enabled);
        }
        
        /**
         * 切换自动转人工状态
         * @param {boolean} enabled - 是否启用
         * @returns {Promise<boolean>} 是否成功
         */
        static async toggleAutoTransfer(enabled = null) {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return await AutomationManager.toggleAutoTransfer(enabled);
        }
        
        /**
         * 检查消息是否需要转人工
         * @param {Object} message - 消息对象
         * @param {Object} state - 会话状态
         * @returns {Promise<boolean>} 是否需要转人工
         */
        static async checkShouldTransfer(message, state) {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return await AutomationManager.checkShouldTransfer(message, state);
        }
        
        /**
         * 注册自动化任务
         * @param {string} id - 任务ID
         * @param {Object} task - 任务配置
         * @returns {boolean} 是否注册成功
         */
        static registerTask(id, task) {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return false;
            }
            
            return AutomationManager.registerTask(id, task);
        }
        
        /**
         * 获取所有任务
         * @returns {Map} 任务Map
         */
        static getTasks() {
            if (!AutomationManager) {
                debug('自动化管理器未加载');
                return new Map();
            }
            
            return AutomationManager.tasks;
        }
        
        /**
         * 获取任务状态
         * @param {string} id - 任务ID
         * @returns {string} 任务状态
         */
        static getTaskState(id) {
            if (!AutomationManager || !AutomationManager.tasks.has(id)) {
                return TaskState.IDLE;
            }
            
            return AutomationManager.tasks.get(id).state;
        }
    }

    // 导出模块
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Core) global.AIPDD.Core = {};
    if (!global.AIPDD.Core.Automation) global.AIPDD.Core.Automation = {};
    
    global.AIPDD.Core.Automation.init = Automation.init;
    global.AIPDD.Core.Automation.start = Automation.start;
    global.AIPDD.Core.Automation.stop = Automation.stop;
    global.AIPDD.Core.Automation.toggleAutoReply = Automation.toggleAutoReply;
    global.AIPDD.Core.Automation.toggleAutoTransfer = Automation.toggleAutoTransfer;
    global.AIPDD.Core.Automation.checkShouldTransfer = Automation.checkShouldTransfer;
    global.AIPDD.Core.Automation.registerTask = Automation.registerTask;
    global.AIPDD.Core.Automation.getTasks = Automation.getTasks;
    global.AIPDD.Core.Automation.getTaskState = Automation.getTaskState;

})(window); 