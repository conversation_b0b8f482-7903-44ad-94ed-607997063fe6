// settings-manager.js - 设置管理器
(function() {
    // 命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Settings = window.AIPDD.Settings || {};
    
    // 设置键名常量
    const KEYS = {
        TRANSFER_SETTINGS: 'transferSettings',
        TRANSFER_KEYWORDS: 'transferKeywords',
        SERVICE_ACCOUNTS: 'serviceAccounts',
        AUTO_REPLY_SETTINGS: 'autoReplySettings',
        AI_SETTINGS: 'aiSettings'
    };
    
    // 默认转人工设置
    const DEFAULT_TRANSFER_SETTINGS = {
        manualEnabled: false,          // 转人工功能开关
        keywordEnabled: true,          // 关键词触发
        imageEnabled: true,            // 图片触发
        refundEnabled: true,           // 退款场景触发
        complaintEnabled: true,        // 投诉场景触发
        serviceAccounts: [],           // 服务账号
        transferErrorOptions: ['message', 'star'],  // 转人工失败处理选项
        transferErrorMessage: '抱歉客服暂时离开，稍后为您处理', // 失败提示消息
        transferFailOptions: ['message'],           // 转人工关闭处理选项
        transferFailMessage: '实在抱歉无法转接，请稍候再联系' // 关闭提示消息
    };
    
    // 设置管理器
    const SettingsManager = {
        /**
         * 获取设置
         * @param {string} key - 设置键名
         * @param {any} defaultValue - 默认值
         * @returns {Promise<any>} 设置值
         */
        getSetting: async function(key, defaultValue) {
            return new Promise((resolve) => {
                chrome.storage.local.get(key, (result) => {
                    resolve(result[key] !== undefined ? result[key] : defaultValue);
                });
            });
        },
        
        /**
         * 更新设置
         * @param {string} key - 设置键名
         * @param {any} value - 设置值
         * @returns {Promise<boolean>} 是否成功
         */
        updateSetting: async function(key, value) {
            return new Promise((resolve) => {
                chrome.storage.local.set({ [key]: value }, () => {
                    resolve(true);
                });
            });
        },
        
        /**
         * 获取转人工设置
         * @returns {Promise<Object>} 转人工设置
         */
        getTransferSettings: async function() {
            return await this.getSetting(KEYS.TRANSFER_SETTINGS, DEFAULT_TRANSFER_SETTINGS);
        },
        
        /**
         * 更新转人工设置
         * @param {Object} settings - 要更新的设置项
         * @returns {Promise<boolean>} 是否成功
         */
        updateTransferSettings: async function(settings) {
            const currentSettings = await this.getTransferSettings();
            const newSettings = { ...currentSettings, ...settings };
            return await this.updateSetting(KEYS.TRANSFER_SETTINGS, newSettings);
        },
        
        /**
         * 获取转人工关键词
         * @returns {Promise<string[]>} 关键词列表
         */
        getTransferKeywords: async function() {
            return await this.getSetting(KEYS.TRANSFER_KEYWORDS, []);
        },
        
        /**
         * 更新转人工关键词
         * @param {string[]} keywords - 关键词列表
         * @returns {Promise<boolean>} 是否成功
         */
        updateTransferKeywords: async function(keywords) {
            return await this.updateSetting(KEYS.TRANSFER_KEYWORDS, keywords);
        },
        
        /**
         * 获取服务账号列表
         * @returns {Promise<Object[]>} 服务账号列表
         */
        getServiceAccounts: async function() {
            return await this.getSetting(KEYS.SERVICE_ACCOUNTS, []);
        },
        
        /**
         * 更新服务账号列表
         * @param {Object[]} accounts - 服务账号列表
         * @returns {Promise<boolean>} 是否成功
         */
        updateServiceAccounts: async function(accounts) {
            return await this.updateSetting(KEYS.SERVICE_ACCOUNTS, accounts);
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Settings = SettingsManager;
    window.AIPDD.Settings.KEYS = KEYS;
    
    // 为了向下兼容，添加StateManager别名
    window.StateManager = {
        getState: async function(key, defaultValue) {
            return await SettingsManager.getSetting(key, defaultValue);
        },
        setState: async function(key, value) {
            return await SettingsManager.updateSetting(key, value);
        }
    };
})();