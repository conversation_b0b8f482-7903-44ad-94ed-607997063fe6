/**
 * 设置管理器
 * 负责加载、保存和管理所有扩展程序的设置
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.Core = window.AIPDD.Core || {};

(function() {
    'use strict';
    
    // 依赖检查
    const Dependencies = {
        Storage: window.AIPDD.Core && window.AIPDD.Core.Storage,
        EventBus: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.EventBus,
        Debug: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug
    };
    
    // 记录日志的辅助函数
    function log(message, level = 'info') {
        if (Dependencies.Debug) {
            Dependencies.Debug.log(message, 'settings-manager', level);
        } else {
            console.log(`[设置管理器] ${message}`);
        }
    }
    
    // 设置类型常量
    const SETTINGS_TYPES = {
        TRANSFER: 'transferSettings',
        AI: 'aiSettings',
        UI: 'uiSettings',
        NOTIFICATION: 'notificationSettings',
        AUTOMATION: 'automationSettings',
        OTHER: 'otherSettings'
    };
    
    // 默认设置
    const DEFAULT_SETTINGS = {
        // 转人工设置
        [SETTINGS_TYPES.TRANSFER]: {
            manualEnabled: true,
            keywordEnabled: true,
            imageEnabled: true,
            refundEnabled: true,
            complaintEnabled: true,
            difyTransferEnabled: true,
            specifiedAgent: '',
            serviceAccounts: [],
            transferFailMarkStar: false,
            transferFailOptions: ['message'],
            transferFailMessage: '实在抱歉无法转接，请稍候再联系',
            transferErrorOptions: ['message'],
            transferErrorMessage: '抱歉客服暂时离开，稍后为您处理'
        },
        
        // AI设置
        [SETTINGS_TYPES.AI]: {
            difyApiKey: '',
            difyAppId: '',
            autoReplyEnabled: false,
            replyDelay: 1000,
            maxRetries: 3
        },
        
        // UI设置
        [SETTINGS_TYPES.UI]: {
            floatingBallEnabled: true,
            floatingBallPosition: { x: 20, y: 20 },
            theme: 'light'
        },
        
        // 通知设置
        [SETTINGS_TYPES.NOTIFICATION]: {
            enabled: true,
            checkInterval: 30000,
            sound: true,
            desktop: false
        },
        
        // 自动化设置
        [SETTINGS_TYPES.AUTOMATION]: {
            healthCheckEnabled: true,
            healthCheckInterval: 60000,
            autoRefreshEnabled: false,
            autoRefreshInterval: 3600000
        },
        
        // 其他设置
        [SETTINGS_TYPES.OTHER]: {
            backupKeywordEnabled: false,
            backupKeywords: [],
            starFlagEnabled: false,
            starFlagKeywords: []
        }
    };
    
    /**
     * 设置管理器类
     */
    class SettingsManager {
        /**
         * 构造函数
         */
        constructor() {
            // 初始化设置缓存
            this.settingsCache = {};
            
            // 绑定方法
            this.getSettings = this.getSettings.bind(this);
            this.saveSettings = this.saveSettings.bind(this);
            this.resetSettings = this.resetSettings.bind(this);
            this.loadAllSettings = this.loadAllSettings.bind(this);
            
            // 注册事件监听
            this._registerEventListeners();
            
            log('设置管理器已初始化');
        }
        
        /**
         * 注册事件监听器
         * @private
         */
        _registerEventListeners() {
            if (Dependencies.EventBus) {
                // 监听设置更新事件
                Dependencies.EventBus.on('settings:save', (data) => {
                    const { type, settings } = data;
                    this.saveSettings(type, settings);
                });
                
                // 监听设置重置事件
                Dependencies.EventBus.on('settings:reset', (data) => {
                    const { type } = data;
                    this.resetSettings(type);
                });
            }
        }
        
        /**
         * 获取设置
         * @param {string} type - 设置类型
         * @returns {Promise<Object>} - 设置对象
         */
        async getSettings(type) {
            try {
                // 检查设置类型是否有效
                if (!Object.values(SETTINGS_TYPES).includes(type)) {
                    throw new Error(`无效的设置类型: ${type}`);
                }
                
                // 如果缓存中有设置，直接返回
                if (this.settingsCache[type]) {
                    return this.settingsCache[type];
                }
                
                // 从存储中获取设置
                let settings;
                if (Dependencies.Storage) {
                    settings = await Dependencies.Storage.get(type, DEFAULT_SETTINGS[type]);
                } else {
                    // 兼容旧版存储
                    settings = await new Promise(resolve => {
                        chrome.storage.local.get(type, (result) => {
                            resolve(result[type] || DEFAULT_SETTINGS[type]);
                        });
                    });
                }
                
                // 更新缓存
                this.settingsCache[type] = settings;
                
                log(`获取设置成功: ${type}`);
                return settings;
            } catch (error) {
                log(`获取设置失败: ${error.message}`, 'error');
                return DEFAULT_SETTINGS[type] || {};
            }
        }
        
        /**
         * 保存设置
         * @param {string} type - 设置类型
         * @param {Object} settings - 设置对象
         * @returns {Promise<boolean>} - 是否保存成功
         */
        async saveSettings(type, settings) {
            try {
                // 检查设置类型是否有效
                if (!Object.values(SETTINGS_TYPES).includes(type)) {
                    throw new Error(`无效的设置类型: ${type}`);
                }
                
                // 合并默认设置
                const mergedSettings = {
                    ...DEFAULT_SETTINGS[type],
                    ...settings
                };
                
                // 保存到存储
                if (Dependencies.Storage) {
                    await Dependencies.Storage.set(type, mergedSettings);
                } else {
                    // 兼容旧版存储
                    await new Promise((resolve, reject) => {
                        chrome.storage.local.set({ [type]: mergedSettings }, () => {
                            if (chrome.runtime.lastError) {
                                reject(chrome.runtime.lastError);
                                return;
                            }
                            resolve();
                        });
                    });
                }
                
                // 更新缓存
                this.settingsCache[type] = mergedSettings;
                
                // 触发设置更新事件
                if (Dependencies.EventBus) {
                    Dependencies.EventBus.emit('settings:updated', {
                        type,
                        settings: mergedSettings
                    });
                }
                
                log(`保存设置成功: ${type}`);
                return true;
            } catch (error) {
                log(`保存设置失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        /**
         * 重置设置
         * @param {string} type - 设置类型
         * @returns {Promise<boolean>} - 是否重置成功
         */
        async resetSettings(type) {
            try {
                // 检查设置类型是否有效
                if (!Object.values(SETTINGS_TYPES).includes(type)) {
                    throw new Error(`无效的设置类型: ${type}`);
                }
                
                // 获取默认设置
                const defaultSettings = DEFAULT_SETTINGS[type];
                
                // 保存默认设置
                await this.saveSettings(type, defaultSettings);
                
                log(`重置设置成功: ${type}`);
                return true;
            } catch (error) {
                log(`重置设置失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        /**
         * 加载所有设置
         * @returns {Promise<Object>} - 所有设置
         */
        async loadAllSettings() {
            try {
                const allSettings = {};
                
                // 加载所有类型的设置
                for (const type of Object.values(SETTINGS_TYPES)) {
                    allSettings[type] = await this.getSettings(type);
                }
                
                log('加载所有设置成功');
                return allSettings;
            } catch (error) {
                log(`加载所有设置失败: ${error.message}`, 'error');
                return {};
            }
        }
        
        /**
         * 初始化设置
         * @returns {Promise<void>}
         */
        async init() {
            try {
                // 预加载所有设置到缓存
                await this.loadAllSettings();
                
                log('设置管理器初始化完成');
            } catch (error) {
                log(`设置管理器初始化失败: ${error.message}`, 'error');
            }
        }
    }
    
    // 创建单例实例
    const instance = new SettingsManager();
    
    // 导出到命名空间
    window.AIPDD.Core.Settings = {
        SettingsManager: instance,
        SETTINGS_TYPES
    };
    
    log('设置管理器模块加载完成');
})(); 