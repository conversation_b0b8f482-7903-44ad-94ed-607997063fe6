// settings-dialog.js
(function() {
    // 依赖
    const Settings = window.AIPDD.Settings;
    
    // 设置对话框
    const TransferSettings = {
        /**
         * 显示设置对话框
         */
        show: function() {
            // 创建对话框
            const dialog = this._createDialog();
            document.body.appendChild(dialog);
            
            // 加载设置
            this._loadSettings(dialog);
            
            // 绑定事件
            this._bindEvents(dialog);
        },
        
        /**
         * 创建对话框
         * @returns {HTMLElement} 对话框元素
         * @private
         */
        _createDialog: function() {
            const dialog = document.createElement('div');
            dialog.className = 'floating-window-overlay';
            dialog.innerHTML = `
                <div class="floating-window transfer-settings">
                    <div class="floating-window-header">
                        <h2>转人工设置</h2>
                        <button class="close-button">×</button>
                    </div>
                    <div class="floating-window-content">
                        <div class="settings-container">
                            <!-- 转人工开关设置 -->
                            <div class="section">
                                <div class="section-title">转人工触发设置</div>
                                <div class="option-group">
                                    <div class="option-item">
                                        <div class="main-switch-label">
                                            自动转人工总开关
                                            <div class="option-description">根据设定的规则自动转接人工客服</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" id="manualEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <div class="option-item">
                                        <div class="option-label">
                                            关键词触发转人工
                                            <div class="option-description">当客户输入内容包含关键词时自动转人工</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" id="keywordEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <div class="option-item">
                                        <div class="option-label">
                                            多媒体消息转人工
                                            <div class="option-description">当客户发送图片/视频触发转人工</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" id="imageEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <div class="option-item">
                                        <div class="option-label">
                                            退款场景转人工
                                            <div class="option-description">涉及退款相关问题时触发转人工</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" id="refundEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <div class="option-item">
                                        <div class="option-label">
                                            投诉场景转人工
                                            <div class="option-description">客户投诉或表达不满时触发转人工</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" id="complaintEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 关键词管理 -->
                            <div class="section">
                                <div class="section-title">转人工关键词</div>
                                <div class="option-group">
                                    <div class="keyword-input-group">
                                        <input type="text" id="newKeyword" placeholder="输入触发转人工关键词">
                                        <button id="addKeyword" class="keyword-btn">添加</button>
                                    </div>
                                    <div id="keywordList" class="keyword-list"></div>
                                </div>
                            </div>
                            
                            <!-- 客服账号设置 -->
                            <div class="section">
                                <div class="section-title">客服账号设置</div>
                                <div class="option-group">
                                    <div class="option-item keyword-management">
                                        <div class="main-switch-label">
                                            转接到指定账号
                                             <div class="option-description">请填客服账号或账号备注</div>
                                        </div>
                                        <div class="keyword-input-group">
                                            <input type="text" id="targetServiceAccount" placeholder="输入当前店铺的一个客服账号">
                                            <button id="addServiceAccount" class="keyword-btn">添加</button>
                                        </div>
                                        <div id="serviceAccountList" class="keyword-list"></div>
                                        <div id="accountError" class="error-message" style="display:none">请至少添加一个客服账号</div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="save-btn" id="saveTransferSettings">保存设置</button>
                            <div id="saveSuccess" class="save-success" style="display:none">
                                <span class="settings-saved-tip">设置已保存</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            return dialog;
        },
        
        /**
         * 加载设置
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        _loadSettings: async function(dialog) {
            try {
                // 获取设置
                const settings = await Settings.getTransferSettings();
                
                // 设置开关状态
                const switches = [
                    'manualEnabled', 'keywordEnabled', 'imageEnabled', 
                    'refundEnabled', 'complaintEnabled'
                ];
                
                switches.forEach(key => {
                    const checkbox = dialog.querySelector(`#${key}`);
                    if (checkbox) {
                        checkbox.checked = settings[key] !== undefined ? settings[key] : true;
                    }
                });
                
                // 加载关键词列表
                await this._loadKeywordList(dialog);
                
                // 加载客服账号列表
                await this._loadServiceAccountList(dialog, settings.serviceAccounts || []);
            } catch (error) {
                console.error('[TransferSettings] 加载设置失败:', error);
            }
        },
        
        /**
         * 加载关键词列表
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        _loadKeywordList: async function(dialog) {
            try {
                // 获取关键词
                const keywords = await Settings.getTransferKeywords();
                
                // 获取列表容器
                const listContainer = dialog.querySelector('#keywordList');
                if (!listContainer) return;
                
                // 清空容器
                listContainer.innerHTML = '';
                
                // 如果没有关键词，显示提示
                if (!keywords || keywords.length === 0) {
                    listContainer.innerHTML = '<div class="keyword-empty-tip">暂无关键词，请添加关键词</div>';
                    return;
                }
                
                // 添加关键词项
                keywords.forEach(keyword => {
                    const item = document.createElement('div');
                    item.className = 'keyword-item';
                    
                    const text = document.createElement('span');
                    text.textContent =