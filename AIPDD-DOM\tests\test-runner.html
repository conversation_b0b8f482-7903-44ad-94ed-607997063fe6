<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIPDD-DOM 单元测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .test-results {
            padding: 20px;
        }
        .test-suite {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .suite-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            cursor: pointer;
        }
        .suite-header:hover {
            background: #e9ecef;
        }
        .suite-content {
            padding: 15px;
        }
        .test-case {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-case.passed {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .test-case.failed {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .test-case.pending {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .test-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-passed {
            background: #28a745;
            color: white;
        }
        .status-failed {
            background: #dc3545;
            color: white;
        }
        .status-pending {
            background: #ffc107;
            color: #212529;
        }
        .error-details {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            color: #dc3545;
        }
        .summary {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .summary-stats {
            display: flex;
            gap: 20px;
        }
        .stat {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 AIPDD-DOM 单元测试</h1>
            <p>Core模块功能测试套件</p>
        </div>
        
        <div class="test-controls">
            <button class="btn btn-primary" onclick="runAllTests()">运行所有测试</button>
            <button class="btn btn-success" onclick="runCoreTests()">Core模块测试</button>
            <button class="btn btn-danger" onclick="clearResults()">清空结果</button>
            <span id="test-status">准备就绪</span>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>
        
        <div class="test-results" id="test-results">
            <p>点击"运行所有测试"开始测试...</p>
        </div>
        
        <div class="summary" id="summary" style="display: none;">
            <div class="summary-stats">
                <div class="stat">
                    <div class="stat-number" id="total-tests">0</div>
                    <div class="stat-label">总测试</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="passed-tests" style="color: #28a745;">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="failed-tests" style="color: #dc3545;">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="pending-tests" style="color: #ffc107;">0</div>
                    <div class="stat-label">待定</div>
                </div>
            </div>
            <div id="test-duration">耗时: 0ms</div>
        </div>
    </div>

    <!-- 模拟Chrome扩展环境 -->
    <script src="test-setup.js"></script>
    
    <!-- 加载Core模块 -->
    <script src="../src/core/utils/debug.js"></script>
    <script src="../src/core/utils/dom-utils.js"></script>
    <script src="../src/core/settings/settings-manager.js"></script>
    <script src="../src/core/storage/storage-manager.js"></script>
    <script src="../src/core/transfer/transfer-rules.js"></script>
    <script src="../src/core/transfer/transfer-manager.js"></script>
    <script src="../src/core/ui/dialog.js"></script>
    
    <!-- 测试框架 -->
    <script src="test-framework.js"></script>
    
    <!-- 测试用例 -->
    <script src="core/utils/debug.test.js"></script>
    <script src="core/utils/dom-utils.test.js"></script>
    <script src="core/settings/settings-manager.test.js"></script>
    <script src="core/transfer/transfer-rules.test.js"></script>
    <script src="core/transfer/transfer-manager.test.js"></script>
    <script src="core/ui/dialog.test.js"></script>
    
    <!-- 测试运行器 -->
    <script src="test-runner.js"></script>
</body>
</html>
