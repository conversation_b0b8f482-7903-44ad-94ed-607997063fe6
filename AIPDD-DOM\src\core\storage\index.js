/**
 * 存储模块入口文件
 * 统一导出存储相关功能
 */

// 确保依赖模块已加载
(function() {
    'use strict';
    
    // 加载存储管理器
    if (!window.AIPDD || !window.AIPDD.StorageManager) {
        console.error('[Storage] 存储管理器未加载');
    }
    
    // 创建命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Core = window.AIPDD.Core || {};
    window.AIPDD.Core.Storage = window.AIPDD.StorageManager;
    
    // 便捷方法
    const StorageAPI = {
        // 基本存储操作
        get: window.AIPDD.StorageManager.get,
        set: window.AIPDD.StorageManager.set,
        remove: window.AIPDD.StorageManager.remove,
        clear: window.AIPDD.StorageManager.clear,
        
        // 高级功能
        getAllKeys: window.AIPDD.StorageManager.getAllKeys,
        checkUsage: window.AIPDD.StorageManager.checkStorageUsage,
        cleanup: window.AIPDD.StorageManager.cleanupStorage,
        
        // 事件监听
        addListener: window.AIPDD.StorageManager.addListener,
        removeListener: window.AIPDD.StorageManager.removeListener,
        
        // 专用存储区域
        settings: {
            get: async function(key, defaultValue) {
                return await window.AIPDD.StorageManager.get(`settings_${key}`, defaultValue);
            },
            set: async function(key, value) {
                return await window.AIPDD.StorageManager.set(`settings_${key}`, value);
            }
        },
        
        messages: {
            get: async function(conversationId, messageId) {
                const messages = await window.AIPDD.StorageManager.get(`messages_${conversationId}`, {});
                return messageId ? messages[messageId] : messages;
            },
            set: async function(conversationId, messageId, value) {
                const messages = await window.AIPDD.StorageManager.get(`messages_${conversationId}`, {});
                messages[messageId] = value;
                return await window.AIPDD.StorageManager.set(`messages_${conversationId}`, messages);
            },
            isReplied: async function(conversationId, messageId) {
                const repliedMessages = await window.AIPDD.StorageManager.get(`replied_messages_${conversationId}`, {});
                return !!repliedMessages[messageId];
            },
            markReplied: async function(conversationId, messageId) {
                const repliedMessages = await window.AIPDD.StorageManager.get(`replied_messages_${conversationId}`, {});
                repliedMessages[messageId] = Date.now();
                return await window.AIPDD.StorageManager.set(`replied_messages_${conversationId}`, repliedMessages);
            }
        },
        
        keywords: {
            get: async function(type) {
                return await window.AIPDD.StorageManager.get(`keywords_${type}`, []);
            },
            set: async function(type, keywords) {
                return await window.AIPDD.StorageManager.set(`keywords_${type}`, keywords);
            },
            add: async function(type, keyword) {
                const keywords = await this.get(type);
                if (!keywords.includes(keyword)) {
                    keywords.push(keyword);
                    return await this.set(type, keywords);
                }
                return keywords;
            },
            remove: async function(type, keyword) {
                const keywords = await this.get(type);
                const index = keywords.indexOf(keyword);
                if (index !== -1) {
                    keywords.splice(index, 1);
                    return await this.set(type, keywords);
                }
                return keywords;
            }
        },
        
        // 初始化方法
        init: function() {
            console.log('[Storage] 存储模块初始化');
            return this;
        }
    };
    
    // 导出到全局命名空间
    window.AIPDD.Storage = StorageAPI;
    
    console.log('[Storage] 存储模块加载完成');
})(); 