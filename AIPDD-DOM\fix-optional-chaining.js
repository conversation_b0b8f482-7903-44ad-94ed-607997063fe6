// 修复可选链操作符的脚本
const fs = require('fs');

function fixOptionalChaining(filePath) {
    console.log(`修复文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let changeCount = 0;
    
    // 修复 obj?.prop 为 obj && obj.prop
    content = content.replace(/(\w+)\?\.([\w\[\]'"]+)/g, (match, obj, prop) => {
        changeCount++;
        return `${obj} && ${obj}.${prop}`;
    });
    
    // 修复 obj?.method() 为 obj && obj.method()
    content = content.replace(/(\w+)\?\.([\w]+)\(/g, (match, obj, method) => {
        changeCount++;
        return `${obj} && ${obj}.${method}(`;
    });
    
    // 修复复杂的可选链，如 obj?.prop?.method()
    // 这需要更复杂的处理，先处理简单情况
    content = content.replace(/(\w+)\?\.([\w]+)\?\.([\w]+)/g, (match, obj, prop1, prop2) => {
        changeCount++;
        return `${obj} && ${obj}.${prop1} && ${obj}.${prop1}.${prop2}`;
    });
    
    // 修复 obj?.prop?.method()
    content = content.replace(/(\w+)\?\.([\w]+)\?\.([\w]+)\(/g, (match, obj, prop, method) => {
        changeCount++;
        return `${obj} && ${obj}.${prop} && ${obj}.${prop}.${method}(`;
    });
    
    // 修复空值合并操作符 ?? 为 ||
    content = content.replace(/\?\?/g, () => {
        changeCount++;
        return '||';
    });
    
    if (changeCount > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  修复了 ${changeCount} 个可选链/空值合并操作符`);
    } else {
        console.log(`  没有发现需要修复的操作符`);
    }
}

// 修复问题文件
const filesToFix = [
    'src/content/content.js'
];

filesToFix.forEach(fixOptionalChaining);
