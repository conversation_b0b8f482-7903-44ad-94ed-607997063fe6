/**
 * 平台管理器
 * 用于注册和管理不同的平台实现
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.Platforms = window.AIPDD.Platforms || {};

(function() {
    'use strict';
    
    // 获取Logger，如果已加载
    const debug = window.AIPDD && window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug ? 
        window.AIPDD.Core.Utils.Debug.log : 
        console.log.bind(console, '[PlatformManager]');
    
    // 获取事件总线
    const EventBus = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.EventBus;
    
    /**
     * 平台管理器类
     */
    class PlatformManager {
        /**
         * 构造函数
         */
        constructor() {
            this.platforms = [];
            this.currentPlatform = null;
            this.isInitialized = false;
            
            // 绑定方法
            this.init = this.init.bind(this);
            this.registerPlatform = this.registerPlatform.bind(this);
            this.detectPlatform = this.detectPlatform.bind(this);
            this.getCurrentPlatform = this.getCurrentPlatform.bind(this);
        }
        
        /**
         * 初始化平台管理器
         * @returns {Promise<boolean>} 初始化是否成功
         */
        async init() {
            debug('平台管理器初始化');
            
            // 注册事件监听
            if (EventBus) {
                EventBus.on('platform:registered', (platform) => {
                    debug(`平台已注册: ${platform.name}`);
                });
            }
            
            // 检测当前平台
            await this.detectPlatform();
            
            this.isInitialized = true;
            return true;
        }
        
        /**
         * 注册平台
         * @param {Object} platform - 平台实例
         * @returns {boolean} 是否注册成功
         */
        registerPlatform(platform) {
            if (!platform) {
                debug('错误: 尝试注册无效的平台');
                return false;
            }
            
            // 检查是否已注册
            const existingPlatform = this.platforms.find(p => p.name === platform.name);
            if (existingPlatform) {
                debug(`平台 ${platform.name} 已注册，跳过`);
                return false;
            }
            
            // 添加到平台列表
            this.platforms.push(platform);
            debug(`平台 ${platform.name} 注册成功`);
            
            // 触发事件
            if (EventBus) {
                EventBus.emit('platform:registered', platform);
            }
            
            return true;
        }
        
        /**
         * 检测当前平台
         * @returns {Promise<Object|null>} 当前平台实例
         */
        async detectPlatform() {
            debug('开始检测当前平台');
            
            // 遍历所有注册的平台
            for (const platform of this.platforms) {
                if (platform.isSupported()) {
                    debug(`检测到平台: ${platform.name}`);
                    
                    // 初始化平台
                    if (!platform.isInitialized) {
                        debug(`初始化平台: ${platform.name}`);
                        await platform.init();
                    }
                    
                    this.currentPlatform = platform;
                    
                    // 触发事件
                    if (EventBus) {
                        EventBus.emit('platform:detected', platform);
                    }
                    
                    return platform;
                }
            }
            
            debug('未检测到支持的平台');
            this.currentPlatform = null;
            return null;
        }
        
        /**
         * 获取当前平台
         * @returns {Object|null} 当前平台实例
         */
        getCurrentPlatform() {
            if (!this.currentPlatform) {
                this.detectPlatform();
            }
            return this.currentPlatform;
        }
        
        /**
         * 获取所有注册的平台
         * @returns {Array} 平台实例数组
         */
        getAllPlatforms() {
            return [...this.platforms];
        }
    }
    
    // 创建实例并导出到命名空间
    const instance = new PlatformManager();
    window.AIPDD.Platforms.PlatformManager = instance;
    
    // 自动初始化
    instance.init().then(() => {
        debug('平台管理器初始化完成');
        
        // 自动注册已知平台
        if (window.AIPDD.Platforms.Pinduoduo && window.AIPDD.Platforms.Pinduoduo.PinduoduoPlatform) {
            instance.registerPlatform(window.AIPDD.Platforms.Pinduoduo.PinduoduoPlatform);
        }
        
        // 触发就绪事件
        if (EventBus) {
            EventBus.emit('platformManager:ready', instance);
        }
    }).catch(error => {
        debug('平台管理器初始化失败:', error);
    });
    
    debug('平台管理器模块已加载');
})(); 