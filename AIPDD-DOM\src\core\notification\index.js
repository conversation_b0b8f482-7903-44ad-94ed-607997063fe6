/**
 * 通知模块入口文件
 * 导出通知管理器和相关功能
 */
(function(global) {
    'use strict';

    // 导入依赖
    const NotificationManager = global.AIPDD?.Core?.Notification?.NotificationManager;
    const NotificationType = global.AIPDD?.Core?.Notification?.NotificationType;
    const { debug } = global.AIPDD?.Core?.Utils?.Debug || { debug: console.log.bind(console, '[Notification]') };

    /**
     * 通知模块
     */
    class Notification {
        /**
         * 初始化通知模块
         * @returns {Promise<boolean>} 是否初始化成功
         */
        static async init() {
            debug('初始化通知模块');
            
            if (!NotificationManager) {
                debug('通知管理器未加载');
                return false;
            }
            
            try {
                // 初始化通知管理器
                const result = await NotificationManager.init();
                
                // 导出兼容API
                if (!global.checkNotification) {
                    global.checkNotification = NotificationManager.checkPinduoduoNotification.bind(NotificationManager);
                }
                
                // 添加全局样式
                Notification.addStyles();
                
                debug('通知模块初始化完成');
                return result;
            } catch (error) {
                debug('初始化通知模块失败:', error);
                return false;
            }
        }
        
        /**
         * 显示通知
         * @param {Object} options - 通知选项
         * @returns {string} 通知ID
         */
        static show(options) {
            if (!NotificationManager) {
                debug('通知管理器未加载');
                return null;
            }
            
            return NotificationManager.show(options);
        }
        
        /**
         * 显示信息通知
         * @param {string} message - 通知内容
         * @param {string} title - 通知标题
         * @param {number} duration - 通知持续时间（毫秒）
         * @returns {string} 通知ID
         */
        static info(message, title, duration = 3000) {
            return Notification.show({
                type: NotificationType.INFO,
                title,
                message,
                duration
            });
        }
        
        /**
         * 显示成功通知
         * @param {string} message - 通知内容
         * @param {string} title - 通知标题
         * @param {number} duration - 通知持续时间（毫秒）
         * @returns {string} 通知ID
         */
        static success(message, title, duration = 3000) {
            return Notification.show({
                type: NotificationType.SUCCESS,
                title,
                message,
                duration
            });
        }
        
        /**
         * 显示警告通知
         * @param {string} message - 通知内容
         * @param {string} title - 通知标题
         * @param {number} duration - 通知持续时间（毫秒）
         * @returns {string} 通知ID
         */
        static warning(message, title, duration = 3000) {
            return Notification.show({
                type: NotificationType.WARNING,
                title,
                message,
                duration
            });
        }
        
        /**
         * 显示错误通知
         * @param {string} message - 通知内容
         * @param {string} title - 通知标题
         * @param {number} duration - 通知持续时间（毫秒）
         * @returns {string} 通知ID
         */
        static error(message, title, duration = 3000) {
            return Notification.show({
                type: NotificationType.ERROR,
                title,
                message,
                duration
            });
        }
        
        /**
         * 显示系统通知
         * @param {string} message - 通知内容
         * @param {string} title - 通知标题
         * @param {number} duration - 通知持续时间（毫秒）
         * @returns {string} 通知ID
         */
        static system(message, title, duration = 3000) {
            return Notification.show({
                type: NotificationType.SYSTEM,
                title,
                message,
                duration
            });
        }
        
        /**
         * 清除通知
         * @param {string} id - 通知ID，如果不指定则清除所有通知
         * @returns {boolean} 是否清除成功
         */
        static clear(id) {
            if (!NotificationManager) {
                debug('通知管理器未加载');
                return false;
            }
            
            return NotificationManager.clear(id);
        }
        
        /**
         * 启动平台通知检查器
         * @param {string} id - 检查器ID，如果不指定则启动所有检查器
         * @returns {Promise<boolean>} 是否启动成功
         */
        static async startPlatformNotifier(id) {
            if (!NotificationManager) {
                debug('通知管理器未加载');
                return false;
            }
            
            if (!id) {
                return await NotificationManager.startAllPlatformNotifiers();
            }
            
            return await NotificationManager.startPlatformNotifier(id);
        }
        
        /**
         * 停止平台通知检查器
         * @param {string} id - 检查器ID，如果不指定则停止所有检查器
         * @returns {Promise<boolean>} 是否停止成功
         */
        static async stopPlatformNotifier(id) {
            if (!NotificationManager) {
                debug('通知管理器未加载');
                return false;
            }
            
            if (!id) {
                return await NotificationManager.stopAllPlatformNotifiers();
            }
            
            return await NotificationManager.stopPlatformNotifier(id);
        }
        
        /**
         * 添加通知样式
         * @private
         */
        static addStyles() {
            // 检查是否已添加样式
            if (document.getElementById('aipdd-notification-styles')) {
                return;
            }
            
            // 创建样式元素
            const style = document.createElement('style');
            style.id = 'aipdd-notification-styles';
            style.textContent = `
                @keyframes notification-slide-in {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                
                .aipdd-notification {
                    transition: all 0.3s;
                }
                
                .aipdd-notification:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
            `;
            
            // 添加到文档头部
            document.head.appendChild(style);
        }
    }

    // 导出模块
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Core) global.AIPDD.Core = {};
    if (!global.AIPDD.Core.Notification) global.AIPDD.Core.Notification = {};
    
    global.AIPDD.Core.Notification.init = Notification.init;
    global.AIPDD.Core.Notification.show = Notification.show;
    global.AIPDD.Core.Notification.info = Notification.info;
    global.AIPDD.Core.Notification.success = Notification.success;
    global.AIPDD.Core.Notification.warning = Notification.warning;
    global.AIPDD.Core.Notification.error = Notification.error;
    global.AIPDD.Core.Notification.system = Notification.system;
    global.AIPDD.Core.Notification.clear = Notification.clear;
    global.AIPDD.Core.Notification.startPlatformNotifier = Notification.startPlatformNotifier;
    global.AIPDD.Core.Notification.stopPlatformNotifier = Notification.stopPlatformNotifier;

})(window); 