// dialog.js - 对话框UI组件模块
(function() {
    // 命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.UI = window.AIPDD.UI || {};
    
    // 对话框组件
    const DialogUI = {
        /**
         * 创建浮动窗口
         * @param {string} title - 窗口标题
         * @param {string} content - 窗口内容
         * @param {Object} options - 选项
         * @returns {Element} 创建的窗口元素
         */
        createFloatingWindow: function(title, content, options = {}) {
            const defaultOptions = {
                closable: true,
                maskClosable: true,
                width: 'auto',
                height: 'auto',
                className: ''
            };
            
            const opts = { ...defaultOptions, ...options };
            
            // 创建遮罩层
            const modal = document.createElement('div');
            modal.className = `floating-window-overlay ${opts.className}`;
            
            // 创建窗口
            const window = document.createElement('div');
            window.className = 'floating-window';
            
            if (opts.width !== 'auto') {
                window.style.width = opts.width;
            }
            if (opts.height !== 'auto') {
                window.style.height = opts.height;
            }
            
            // 构建窗口HTML
            let windowHTML = `
                <div class="floating-window-header">
                    <h2>${title}</h2>
                    ${opts.closable ? '<button class="close-button">×</button>' : ''}
                </div>
                <div class="floating-window-content">
                    ${content}
                </div>
            `;
            
            window.innerHTML = windowHTML;
            modal.appendChild(window);
            document.body.appendChild(modal);
            
            // 添加关闭事件
            if (opts.closable) {
                const closeButton = window.querySelector('.close-button');
                if (closeButton) {
                    closeButton.onclick = () => {
                        this.closeDialog(modal);
                    };
                }
            }
            
            // 点击遮罩层关闭
            if (opts.maskClosable) {
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        this.closeDialog(modal);
                    }
                };
            }
            
            return modal;
        },
        
        /**
         * 创建确认对话框
         * @param {string} title - 标题
         * @param {string} message - 消息
         * @param {Function} onConfirm - 确认回调
         * @param {Function} onCancel - 取消回调
         * @returns {Element} 对话框元素
         */
        createConfirmDialog: function(title, message, onConfirm, onCancel) {
            const content = `
                <div class="confirm-dialog-content">
                    <p>${message}</p>
                    <div class="confirm-dialog-buttons">
                        <button class="confirm-btn">确认</button>
                        <button class="cancel-btn">取消</button>
                    </div>
                </div>
            `;
            
            const dialog = this.createFloatingWindow(title, content, {
                className: 'confirm-dialog'
            });
            
            // 绑定按钮事件
            const confirmBtn = dialog.querySelector('.confirm-btn');
            const cancelBtn = dialog.querySelector('.cancel-btn');
            
            if (confirmBtn) {
                confirmBtn.onclick = () => {
                    if (onConfirm) onConfirm();
                    this.closeDialog(dialog);
                };
            }
            
            if (cancelBtn) {
                cancelBtn.onclick = () => {
                    if (onCancel) onCancel();
                    this.closeDialog(dialog);
                };
            }
            
            return dialog;
        },
        
        /**
         * 创建消息对话框
         * @param {string} title - 标题
         * @param {string} message - 消息
         * @param {string} type - 类型 (info, success, warning, error)
         * @param {Function} onClose - 关闭回调
         * @returns {Element} 对话框元素
         */
        createMessageDialog: function(title, message, type = 'info', onClose) {
            const iconMap = {
                info: 'ℹ️',
                success: '✅',
                warning: '⚠️',
                error: '❌'
            };
            
            const content = `
                <div class="message-dialog-content ${type}">
                    <div class="message-icon">${iconMap[type] || iconMap.info}</div>
                    <div class="message-text">${message}</div>
                    <div class="message-dialog-buttons">
                        <button class="ok-btn">确定</button>
                    </div>
                </div>
            `;
            
            const dialog = this.createFloatingWindow(title, content, {
                className: `message-dialog ${type}`
            });
            
            // 绑定确定按钮事件
            const okBtn = dialog.querySelector('.ok-btn');
            if (okBtn) {
                okBtn.onclick = () => {
                    if (onClose) onClose();
                    this.closeDialog(dialog);
                };
            }
            
            return dialog;
        },
        
        /**
         * 创建输入对话框
         * @param {string} title - 标题
         * @param {string} message - 消息
         * @param {string} defaultValue - 默认值
         * @param {Function} onConfirm - 确认回调
         * @param {Function} onCancel - 取消回调
         * @returns {Element} 对话框元素
         */
        createInputDialog: function(title, message, defaultValue = '', onConfirm, onCancel) {
            const content = `
                <div class="input-dialog-content">
                    <p>${message}</p>
                    <input type="text" class="input-field" value="${defaultValue}" placeholder="请输入...">
                    <div class="input-dialog-buttons">
                        <button class="confirm-btn">确认</button>
                        <button class="cancel-btn">取消</button>
                    </div>
                </div>
            `;
            
            const dialog = this.createFloatingWindow(title, content, {
                className: 'input-dialog'
            });
            
            const inputField = dialog.querySelector('.input-field');
            const confirmBtn = dialog.querySelector('.confirm-btn');
            const cancelBtn = dialog.querySelector('.cancel-btn');
            
            // 聚焦输入框
            if (inputField) {
                setTimeout(() => inputField.focus(), 100);
            }
            
            // 绑定按钮事件
            if (confirmBtn) {
                confirmBtn.onclick = () => {
                    const value = inputField ? inputField.value : '';
                    if (onConfirm) onConfirm(value);
                    this.closeDialog(dialog);
                };
            }
            
            if (cancelBtn) {
                cancelBtn.onclick = () => {
                    if (onCancel) onCancel();
                    this.closeDialog(dialog);
                };
            }
            
            // 回车确认
            if (inputField) {
                inputField.onkeypress = (e) => {
                    if (e.key === 'Enter') {
                        confirmBtn.click();
                    }
                };
            }
            
            return dialog;
        },
        
        /**
         * 关闭对话框
         * @param {Element} dialog - 对话框元素
         */
        closeDialog: function(dialog) {
            if (dialog && dialog.parentNode) {
                dialog.parentNode.removeChild(dialog);
            }
        },
        
        /**
         * 关闭所有对话框
         */
        closeAllDialogs: function() {
            const dialogs = document.querySelectorAll('.floating-window-overlay');
            dialogs.forEach(dialog => this.closeDialog(dialog));
        },
        
        /**
         * 显示加载提示
         * @param {string} message - 提示消息
         * @returns {Element} 加载对话框元素
         */
        showLoading: function(message = '加载中...') {
            const content = `
                <div class="loading-dialog-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `;
            
            return this.createFloatingWindow('', content, {
                closable: false,
                maskClosable: false,
                className: 'loading-dialog'
            });
        },
        
        /**
         * 隐藏加载提示
         * @param {Element} loadingDialog - 加载对话框元素
         */
        hideLoading: function(loadingDialog) {
            this.closeDialog(loadingDialog);
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.UI.Dialog = DialogUI;
    
    // 为了向下兼容，暴露常用函数到全局
    window.createFloatingWindow = DialogUI.createFloatingWindow;
    window.createConfirmDialog = DialogUI.createConfirmDialog;
    window.createMessageDialog = DialogUI.createMessageDialog;
})();
