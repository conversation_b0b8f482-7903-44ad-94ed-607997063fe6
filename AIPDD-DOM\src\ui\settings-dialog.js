 /**
 * 设置对话框UI组件
 * 负责显示和管理各种设置窗口
 */
(function() {
    'use strict';

    // 确保命名空间存在
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.UI = window.AIPDD.UI || {};

    // 依赖检查
    const Dependencies = {
        Debug: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug,
        EventBus: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.EventBus,
        Storage: window.AIPDD.Core && window.AIPDD.Core.Storage && window.AIPDD.Core.Storage.StorageManager
    };

    // 记录日志的辅助函数
    function log(message, level = 'info') {
        if (Dependencies.Debug) {
            Dependencies.Debug.log(message, 'settings-dialog', level);
        } else {
            console.log(`[设置对话框] ${message}`);
        }
    }

    /**
     * 设置对话框类
     * 管理设置窗口的显示和交互
     */
    class SettingsDialog {
        /**
         * 构造函数
         */
        constructor() {
            this.dialogs = {
                settings: null,
                aiSettings: null,
                otherSettings: null,
                about: null
            };
            
            // 绑定事件处理器
            this._onDialogClick = this._onDialogClick.bind(this);
            
            // 注册事件监听
            this._registerEventListeners();
            
            log('设置对话框组件已初始化');
        }

        /**
         * 注册事件监听器
         * @private
         */
        _registerEventListeners() {
            if (Dependencies.EventBus) {
                // 使用事件总线注册事件
                Dependencies.EventBus.on('settings:show', () => this.showSettings());
                Dependencies.EventBus.on('ai-settings:show', () => this.showAISettings());
                Dependencies.EventBus.on('other-settings:show', () => this.showOtherSettings());
                Dependencies.EventBus.on('about:show', () => this.showAbout());
            } else {
                // 使用自定义事件作为备选
                document.addEventListener('AIPDD:settings:show', () => this.showSettings());
                document.addEventListener('AIPDD:ai-settings:show', () => this.showAISettings());
                document.addEventListener('AIPDD:other-settings:show', () => this.showOtherSettings());
                document.addEventListener('AIPDD:about:show', () => this.showAbout());
            }
        }

        /**
         * 加载样式
         * @private
         */
        _loadStyles() {
            try {
                // 检查样式是否已加载
                if (document.querySelector('#aipdd-dialog-styles')) {
                    return;
                }
                
                // 创建样式元素
                const style = document.createElement('link');
                style.id = 'aipdd-dialog-styles';
                style.rel = 'stylesheet';
                style.type = 'text/css';
                style.href = chrome.runtime.getURL('src/styles/floating-ball.css');
                
                // 添加到页面
                document.head.appendChild(style);
                
                log('对话框样式已加载');
            } catch (error) {
                log(`加载样式失败: ${error.message}`, 'error');
            }
        }

        /**
         * 创建对话框基础结构
         * @param {string} id - 对话框ID
         * @param {string} title - 对话框标题
         * @param {string} content - 对话框内容HTML
         * @param {boolean} hasFooter - 是否有底部按钮区域
         * @returns {HTMLElement} 对话框元素
         * @private
         */
        _createDialogBase(id, title, content, hasFooter = true) {
            // 创建对话框容器
            const dialog = document.createElement('div');
            dialog.className = 'settings-dialog';
            dialog.id = id;
            
            // 创建对话框内容
            let dialogHTML = `
                <div class="settings-content">
                    <div class="settings-header">
                        <h3>${title}</h3>
                        <span class="close-btn">&times;</span>
                    </div>
                    <div class="settings-body">
                        ${content}
                    </div>
            `;
            
            // 添加底部按钮区域
            if (hasFooter) {
                dialogHTML += `
                    <div class="settings-footer">
                        <button class="cancel-btn">取消</button>
                        <button class="save-btn">保存</button>
                    </div>
                `;
            }
            
            dialogHTML += `</div>`;
            dialog.innerHTML = dialogHTML;
            
            // 添加点击事件
            dialog.addEventListener('click', this._onDialogClick);
            
            return dialog;
        }

        /**
         * 对话框点击事件处理
         * @param {MouseEvent} e - 鼠标事件
         * @private
         */
        _onDialogClick(e) {
            const target = e.target;
            
            // 处理关闭按钮点击
            if (target.classList.contains('close-btn') || 
                target.classList.contains('cancel-btn') || 
                (target.classList.contains('settings-dialog') && !target.closest('.settings-content'))) {
                this.closeDialog(target.closest('.settings-dialog'));
                return;
            }
            
            // 处理保存按钮点击
            if (target.classList.contains('save-btn')) {
                const dialog = target.closest('.settings-dialog');
                if (dialog) {
                    const dialogId = dialog.id;
                    switch (dialogId) {
                        case 'settings-dialog':
                            this._saveSettings(dialog);
                            break;
                        case 'ai-settings-dialog':
                            this._saveAISettings(dialog);
                            break;
                        case 'other-settings-dialog':
                            this._saveOtherSettings(dialog);
                            break;
                    }
                }
            }
        }

        /**
         * 创建并显示对话框
         * @param {string} id - 对话框ID
         * @param {string} title - 对话框标题
         * @param {string} content - 对话框内容HTML
         * @param {boolean} hasFooter - 是否有底部按钮区域
         * @private
         */
        _createAndShowDialog(id, title, content, hasFooter = true) {
            // 确保样式已加载
            this._loadStyles();
            
            // 关闭其他对话框
            this.closeAllDialogs();
            
            // 创建对话框
            const dialog = this._createDialogBase(id, title, content, hasFooter);
            
            // 添加到页面
            document.body.appendChild(dialog);
            
            // 强制重绘以触发动画
            dialog.offsetHeight;
            
            // 保存对话框引用
            this.dialogs[id.replace('-dialog', '')] = dialog;
            
            log(`显示对话框: ${id}`);
            
            return dialog;
        }

        /**
         * 关闭指定对话框
         * @param {HTMLElement} dialog - 对话框元素
         */
        closeDialog(dialog) {
            if (!dialog) return;
            
            // 移除对话框
            dialog.remove();
            
            // 清除对话框引用
            for (const key in this.dialogs) {
                if (this.dialogs[key] === dialog) {
                    this.dialogs[key] = null;
                    break;
                }
            }
            
            log(`关闭对话框: ${dialog.id}`);
        }

        /**
         * 关闭所有对话框
         */
        closeAllDialogs() {
            for (const key in this.dialogs) {
                if (this.dialogs[key]) {
                    this.dialogs[key].remove();
                    this.dialogs[key] = null;
                }
            }
            
            log('关闭所有对话框');
        }

        /**
         * 显示主设置窗口
         */
        async showSettings() {
            try {
                // 先加载设置数据
                const settings = await this._loadSettings();
                
                // 创建设置窗口内容
                const content = this._createSettingsContent(settings);
                
                // 显示对话框
                this._createAndShowDialog('settings-dialog', '转人工设置', content);
                
                // 初始化关键词列表
                this._initKeywordList();
                
                log('显示转人工设置窗口');
            } catch (error) {
                log(`显示设置窗口失败: ${error.message}`, 'error');
            }
        }

        /**
         * 创建设置窗口内容
         * @param {Object} settings - 设置数据
         * @returns {string} 设置窗口HTML内容
         * @private
         */
        _createSettingsContent(settings) {
            return `
                <div class="section">
                    <div class="section-title">转人工功能</div>
                    <div class="option-group">
                        <div class="option-item">
                            <label class="option-label">
                                <div>启用转人工</div>
                                <div class="option-description">开启后，符合条件的消息将自动转人工</div>
                            </label>
                            <label class="switch">
                                <input type="checkbox" id="transferEnabled" ${settings.transferEnabled ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="section">
                    <div class="section-title">转人工触发条件</div>
                    <div class="option-group">
                        <div class="option-item">
                            <label class="option-label">
                                <div>关键词触发</div>
                                <div class="option-description">当消息中包含以下关键词时触发转人工</div>
                            </label>
                        </div>
                        <div class="keyword-group">
                            <div class="keyword-input">
                                <input type="text" id="newKeyword" placeholder="输入关键词">
                                <button id="addKeyword">添加</button>
                            </div>
                            <div class="keyword-list" id="keywordList">
                                <!-- 关键词列表将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section">
                    <div class="section-title">转人工设置</div>
                    <div class="option-group">
                        <div class="option-item">
                            <label class="option-label">
                                <div>选择客服账号</div>
                                <div class="option-description">选择要转接的客服账号</div>
                            </label>
                            <select id="serviceAccount">
                                <option value="">自动选择</option>
                                <!-- 客服账号列表将动态生成 -->
                            </select>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 初始化关键词列表
         * @private
         */
        async _initKeywordList() {
            try {
                // 获取关键词列表容器
                const keywordList = document.querySelector('#keywordList');
                if (!keywordList) return;
                
                // 清空列表
                keywordList.innerHTML = '';
                
                // 加载关键词
                const keywords = await this._loadTransferKeywords();
                
                // 添加关键词到列表
                for (const keyword of keywords) {
                    this._addKeywordToList(keyword);
                }
                
                // 绑定添加关键词事件
                const addButton = document.querySelector('#addKeyword');
                const input = document.querySelector('#newKeyword');
                
                if (addButton && input) {
                    addButton.addEventListener('click', () => {
                        const keyword = input.value.trim();
                        if (keyword) {
                            this._addKeywordToList(keyword);
                            input.value = '';
                        }
                    });
                    
                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            const keyword = input.value.trim();
                            if (keyword) {
                                this._addKeywordToList(keyword);
                                input.value = '';
                            }
                        }
                    });
                }
                
                log('初始化关键词列表完成');
            } catch (error) {
                log(`初始化关键词列表失败: ${error.message}`, 'error');
            }
        }

        /**
         * 添加关键词到列表
         * @param {string} keyword - 关键词
         * @private
         */
        _addKeywordToList(keyword) {
            // 获取关键词列表容器
            const keywordList = document.querySelector('#keywordList');
            if (!keywordList) return;
            
            // 创建关键词元素
            const keywordItem = document.createElement('div');
            keywordItem.className = 'keyword-item';
            keywordItem.dataset.keyword = keyword;
            keywordItem.innerHTML = `
                <span>${keyword}</span>
                <span class="remove-keyword">&times;</span>
            `;
            
            // 添加删除事件
            const removeBtn = keywordItem.querySelector('.remove-keyword');
            if (removeBtn) {
                removeBtn.addEventListener('click', () => {
                    keywordItem.remove();
                });
            }
            
            // 添加到列表
            keywordList.appendChild(keywordItem);
        }

        /**
         * 加载转人工关键词
         * @returns {Promise<string[]>} 关键词列表
         * @private
         */
        async _loadTransferKeywords() {
            try {
                if (Dependencies.Storage) {
                    return await Dependencies.Storage.get('transferKeywords') || [];
                } else {
                    // 兼容旧版存储
                    return await new Promise(resolve => {
                        chrome.storage.local.get(['transferKeywords'], function(result) {
                            resolve(result.transferKeywords || []);
                        });
                    });
                }
            } catch (error) {
                log(`加载转人工关键词失败: ${error.message}`, 'error');
                return [];
            }
        }

        /**
         * 加载设置数据
         * @returns {Promise<Object>} 设置数据
         * @private
         */
        async _loadSettings() {
            try {
                let settings = {
                    transferEnabled: false,
                    serviceAccount: ''
                };
                
                if (Dependencies.Storage) {
                    const storedSettings = await Dependencies.Storage.get('transferSettings');
                    if (storedSettings) {
                        settings = { ...settings, ...storedSettings };
                    }
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.get(['transferEnabled', 'serviceAccount'], function(result) {
                            if (result.transferEnabled !== undefined) {
                                settings.transferEnabled = result.transferEnabled;
                            }
                            if (result.serviceAccount !== undefined) {
                                settings.serviceAccount = result.serviceAccount;
                            }
                            resolve();
                        });
                    });
                }
                
                return settings;
            } catch (error) {
                log(`加载设置失败: ${error.message}`, 'error');
                return {
                    transferEnabled: false,
                    serviceAccount: ''
                };
            }
        }

        /**
         * 保存设置
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        async _saveSettings(dialog) {
            try {
                // 获取表单数据
                const transferEnabled = dialog.querySelector('#transferEnabled').checked;
                const serviceAccount = dialog.querySelector('#serviceAccount').value;
                
                // 获取关键词
                const keywordItems = dialog.querySelectorAll('.keyword-item');
                const keywords = Array.from(keywordItems).map(item => item.dataset.keyword);
                
                // 保存设置
                const settings = {
                    transferEnabled,
                    serviceAccount
                };
                
                if (Dependencies.Storage) {
                    await Dependencies.Storage.set('transferSettings', settings);
                    await Dependencies.Storage.set('transferKeywords', keywords);
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.set({
                            'transferEnabled': transferEnabled,
                            'serviceAccount': serviceAccount,
                            'transferKeywords': keywords
                        }, resolve);
                    });
                }
                
                // 显示保存成功提示
                this._showSaveSuccess();
                
                // 关闭对话框
                setTimeout(() => {
                    this.closeDialog(dialog);
                }, 1000);
                
                // 触发设置变更事件
                this._triggerEvent('settings:changed', { transferEnabled, serviceAccount, keywords });
                
                log('保存设置成功');
            } catch (error) {
                log(`保存设置失败: ${error.message}`, 'error');
            }
        }

        /**
         * 显示保存成功提示
         * @private
         */
        _showSaveSuccess() {
            // 检查是否已存在提示
            let successTip = document.querySelector('.save-success');
            
            // 如果不存在，创建一个
            if (!successTip) {
                successTip = document.createElement('div');
                successTip.className = 'save-success';
                successTip.textContent = '保存成功';
                document.body.appendChild(successTip);
            }
            
            // 显示提示
            successTip.classList.add('visible');
            
            // 1.5秒后隐藏
            setTimeout(() => {
                successTip.classList.remove('visible');
            }, 1500);
        }

        /**
         * 触发事件
         * @param {string} eventName - 事件名称
         * @param {Object} data - 事件数据
         * @private
         */
        _triggerEvent(eventName, data = {}) {
            if (Dependencies.EventBus) {
                Dependencies.EventBus.emit(eventName, data);
            } else {
                // 作为临时解决方案，可以使用自定义事件
                const event = new CustomEvent(`AIPDD:${eventName}`, { detail: data });
                document.dispatchEvent(event);
            }
        }

        /**
         * 显示AI设置窗口
         */
        showAISettings() {
            // 创建AI设置窗口内容
            const content = `
                <div class="section">
                    <div class="section-title">AI回复设置</div>
                    <div class="option-group">
                        <div class="option-item">
                            <label class="option-label">
                                <div>Dify API密钥</div>
                                <div class="option-description">设置Dify API密钥，用于AI回复</div>
                            </label>
                            <input type="text" id="difyApiKey" placeholder="输入API密钥">
                        </div>
                        <div class="option-item">
                            <label class="option-label">
                                <div>关联应用ID</div>
                                <div class="option-description">关联的Dify应用ID</div>
                            </label>
                            <input type="text" id="difyAppId" placeholder="输入应用ID">
                        </div>
                    </div>
                </div>
                <div id="aiSettingsSaveSuccess" style="display: none;">
                    设置已保存
                </div>
            `;
            
            // 显示对话框
            const dialog = this._createAndShowDialog('ai-settings-dialog', 'AI设置', content);
            
            // 加载AI设置数据
            this._loadAISettings(dialog);
            
            log('显示AI设置窗口');
        }

        /**
         * 加载AI设置数据
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        async _loadAISettings(dialog) {
            try {
                let apiKey = '';
                let appId = '';
                
                if (Dependencies.Storage) {
                    apiKey = await Dependencies.Storage.get('difyApiKey', '');
                    appId = await Dependencies.Storage.get('difyAppId', '');
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.get(['difyApiKey', 'difyAppId'], function(result) {
                            apiKey = result.difyApiKey || '';
                            appId = result.difyAppId || '';
                            resolve();
                        });
                    });
                }
                
                // 设置输入框值
                const apiKeyInput = dialog.querySelector('#difyApiKey');
                const appIdInput = dialog.querySelector('#difyAppId');
                
                if (apiKeyInput) apiKeyInput.value = apiKey;
                if (appIdInput) appIdInput.value = appId;
                
                log('加载AI设置数据成功');
            } catch (error) {
                log(`加载AI设置数据失败: ${error.message}`, 'error');
            }
        }

        /**
         * 保存AI设置
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        async _saveAISettings(dialog) {
            try {
                // 获取表单数据
                const apiKey = dialog.querySelector('#difyApiKey').value.trim();
                const appId = dialog.querySelector('#difyAppId').value.trim();
                
                // 保存设置
                if (Dependencies.Storage) {
                    await Dependencies.Storage.set('difyApiKey', apiKey);
                    await Dependencies.Storage.set('difyAppId', appId);
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.set({
                            'difyApiKey': apiKey,
                            'difyAppId': appId
                        }, resolve);
                    });
                }
                
                // 显示保存成功提示
                this._showSaveSuccess();
                
                // 关闭对话框
                setTimeout(() => {
                    this.closeDialog(dialog);
                }, 1000);
                
                // 触发设置变更事件
                this._triggerEvent('ai-settings:changed', { apiKey, appId });
                
                log('保存AI设置成功');
            } catch (error) {
                log(`保存AI设置失败: ${error.message}`, 'error');
            }
        }

        /**
         * 显示其他设置窗口
         */
        showOtherSettings() {
            // 创建其他设置窗口内容
            const content = `
                <div class="section">
                    <div class="section-title">附加功能设置</div>
                    <div class="option-group">
                        <div class="option-item">
                            <label class="option-label">
                                <div>备用关键词功能</div>
                                <div class="option-description">启用后，可以设置备用关键词处理规则</div>
                            </label>
                            <label class="switch">
                                <input type="checkbox" id="backupKeywordEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="option-item">
                            <label class="option-label">
                                <div>订单标记功能</div>
                                <div class="option-description">启用后，可以设置订单标记规则</div>
                            </label>
                            <label class="switch">
                                <input type="checkbox" id="starFlagEnabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            `;
            
            // 显示对话框
            const dialog = this._createAndShowDialog('other-settings-dialog', '其他设置', content);
            
            // 加载其他设置数据
            this._loadOtherSettings(dialog);
            
            log('显示其他设置窗口');
        }

        /**
         * 加载其他设置数据
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        async _loadOtherSettings(dialog) {
            try {
                let backupKeywordEnabled = false;
                let starFlagEnabled = false;
                
                if (Dependencies.Storage) {
                    backupKeywordEnabled = await Dependencies.Storage.get('backupKeywordEnabled', false);
                    starFlagEnabled = await Dependencies.Storage.get('starFlagEnabled', false);
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.get(['backupKeywordEnabled', 'starFlagEnabled'], function(result) {
                            backupKeywordEnabled = result.backupKeywordEnabled || false;
                            starFlagEnabled = result.starFlagEnabled || false;
                            resolve();
                        });
                    });
                }
                
                // 设置复选框状态
                const backupKeywordCheckbox = dialog.querySelector('#backupKeywordEnabled');
                const starFlagCheckbox = dialog.querySelector('#starFlagEnabled');
                
                if (backupKeywordCheckbox) backupKeywordCheckbox.checked = backupKeywordEnabled;
                if (starFlagCheckbox) starFlagCheckbox.checked = starFlagEnabled;
                
                log('加载其他设置数据成功');
            } catch (error) {
                log(`加载其他设置数据失败: ${error.message}`, 'error');
            }
        }

        /**
         * 保存其他设置
         * @param {HTMLElement} dialog - 对话框元素
         * @private
         */
        async _saveOtherSettings(dialog) {
            try {
                // 获取表单数据
                const backupKeywordEnabled = dialog.querySelector('#backupKeywordEnabled').checked;
                const starFlagEnabled = dialog.querySelector('#starFlagEnabled').checked;
                
                // 保存设置
                if (Dependencies.Storage) {
                    await Dependencies.Storage.set('backupKeywordEnabled', backupKeywordEnabled);
                    await Dependencies.Storage.set('starFlagEnabled', starFlagEnabled);
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.set({
                            'backupKeywordEnabled': backupKeywordEnabled,
                            'starFlagEnabled': starFlagEnabled
                        }, resolve);
                    });
                }
                
                // 显示保存成功提示
                this._showSaveSuccess();
                
                // 关闭对话框
                setTimeout(() => {
                    this.closeDialog(dialog);
                }, 1000);
                
                // 触发设置变更事件
                this._triggerEvent('other-settings:changed', { backupKeywordEnabled, starFlagEnabled });
                
                log('保存其他设置成功');
            } catch (error) {
                log(`保存其他设置失败: ${error.message}`, 'error');
            }
        }

        /**
         * 显示关于窗口
         */
        showAbout() {
            // 获取版本信息
            const manifest = chrome.runtime.getManifest();
            const version = manifest.version || '1.0.0';
            
            // 创建关于窗口内容
            const content = `
                <div class="about-body">
                    <img src="${chrome.runtime.getURL('icons/icon128.png')}" alt="AIPDD-DOM" width="80" height="80">
                    <h2>AIPDD-DOM</h2>
                    <p>AI客服助手</p>
                    <p>版本: ${version}</p>
                    <p>基于AI技术的客服自动回复工具</p>
                    <p style="margin-top: 20px;">© 2023-2025 All Rights Reserved</p>
                </div>
            `;
            
            // 显示对话框
            this._createAndShowDialog('about-dialog', '关于', content, false);
            
            log('显示关于窗口');
        }
    }

    // 导出到命名空间
    window.AIPDD.UI.SettingsDialog = new SettingsDialog();

    // 调试日志
    log('设置对话框模块已加载');
})();