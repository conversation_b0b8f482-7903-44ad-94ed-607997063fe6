/**
 * 存储管理器
 * 提供统一的存储接口，支持本地存储和云端存储
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.StorageManager = (function() {
    'use strict';
    
    // 私有变量
    const debug = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.Debug ? 
        window.AIPDD.Utils.Debug.log : 
        console.log.bind(console, '[StorageManager]');
    
    // 事件总线
    const EventBus = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.EventBus;
    
    // 存储配置
    const config = {
        // 存储前缀，用于区分不同的存储命名空间
        prefix: 'aipdd_',
        
        // 存储类型
        storageType: 'local', // 'local' 或 'sync'
        
        // 存储限制（字节）
        storageLimits: {
            local: 5 * 1024 * 1024, // Chrome本地存储限制约为5MB
            sync: 100 * 1024 // Chrome同步存储限制约为100KB
        },
        
        // 默认过期时间（毫秒）
        defaultExpiration: 7 * 24 * 60 * 60 * 1000, // 7天
        
        // 存储清理阈值（使用率百分比）
        cleanupThreshold: 80
    };
    
    // 存储监听器集合
    const listeners = new Set();
    
    /**
     * 获取完整存储键（添加前缀）
     * @param {string} key - 原始键
     * @returns {string} - 添加前缀后的键
     */
    function getFullKey(key) {
        return `${config.prefix}${key}`;
    }
    
    /**
     * 获取存储值
     * @param {string} key - 存储键
     * @param {*} defaultValue - 默认值
     * @returns {Promise<*>} - 存储值
     */
    async function get(key, defaultValue = null) {
        return new Promise((resolve) => {
            const fullKey = getFullKey(key);
            chrome.storage[config.storageType].get(fullKey, (result) => {
                const value = result[fullKey];
                
                // 检查值是否存在
                if (value === undefined) {
                    debug(`键 ${key} 不存在，返回默认值`);
                    resolve(defaultValue);
                    return;
                }
                
                // 检查值是否过期
                if (value && value._expires && value._expires < Date.now()) {
                    debug(`键 ${key} 已过期，返回默认值`);
                    // 异步删除过期值
                    remove(key).catch(err => debug('删除过期值出错:', err));
                    resolve(defaultValue);
                    return;
                }
                
                // 返回实际值
                resolve(value && value._data !== undefined ? value._data : value);
            });
        });
    }
    
    /**
     * 设置存储值
     * @param {string} key - 存储键
     * @param {*} value - 存储值
     * @param {Object} options - 选项
     * @param {number} options.expiration - 过期时间（毫秒）
     * @returns {Promise<void>}
     */
    async function set(key, value, options = {}) {
        return new Promise((resolve, reject) => {
            const fullKey = getFullKey(key);
            
            // 检查存储空间
            checkStorageUsage().then(async (usage) => {
                // 如果存储空间使用率超过阈值，执行清理
                if (usage.percentUsed > config.cleanupThreshold) {
                    debug(`存储空间使用率(${usage.percentUsed}%)超过阈值(${config.cleanupThreshold}%)，执行清理`);
                    await cleanupStorage();
                }
                
                // 准备存储数据
                let dataToStore = value;
                
                // 如果设置了过期时间，包装数据
                if (options.expiration || config.defaultExpiration) {
                    const expiration = options.expiration || config.defaultExpiration;
                    dataToStore = {
                        _data: value,
                        _expires: Date.now() + expiration,
                        _created: Date.now()
                    };
                }
                
                // 存储数据
                chrome.storage[config.storageType].set({ [fullKey]: dataToStore }, () => {
                    if (chrome.runtime.lastError) {
                        debug('存储错误:', chrome.runtime.lastError);
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    
                    debug(`成功存储键 ${key}`);
                    
                    // 通知监听器
                    notifyListeners(key, value);
                    
                    // 触发事件
                    if (EventBus) {
                        EventBus.emit('storage:changed', { key, value });
                    }
                    
                    resolve();
                });
            }).catch(reject);
        });
    }
    
    /**
     * 删除存储值
     * @param {string} key - 存储键
     * @returns {Promise<void>}
     */
    async function remove(key) {
        return new Promise((resolve, reject) => {
            const fullKey = getFullKey(key);
            chrome.storage[config.storageType].remove(fullKey, () => {
                if (chrome.runtime.lastError) {
                    debug('删除错误:', chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                    return;
                }
                
                debug(`成功删除键 ${key}`);
                
                // 通知监听器
                notifyListeners(key, undefined);
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('storage:removed', { key });
                }
                
                resolve();
            });
        });
    }
    
    /**
     * 清空所有存储
     * @returns {Promise<void>}
     */
    async function clear() {
        return new Promise((resolve, reject) => {
            chrome.storage[config.storageType].clear(() => {
                if (chrome.runtime.lastError) {
                    debug('清空存储错误:', chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                    return;
                }
                
                debug('成功清空所有存储');
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('storage:cleared');
                }
                
                resolve();
            });
        });
    }
    
    /**
     * 获取所有存储键
     * @returns {Promise<string[]>} - 键列表
     */
    async function getAllKeys() {
        return new Promise((resolve) => {
            chrome.storage[config.storageType].get(null, (items) => {
                const keys = Object.keys(items)
                    .filter(key => key.startsWith(config.prefix))
                    .map(key => key.substring(config.prefix.length));
                resolve(keys);
            });
        });
    }
    
    /**
     * 检查存储空间使用情况
     * @returns {Promise<Object>} - 使用情况对象
     */
    async function checkStorageUsage() {
        return new Promise((resolve) => {
            chrome.storage[config.storageType].getBytesInUse(null, (bytesInUse) => {
                const limit = config.storageLimits[config.storageType];
                const percentUsed = Math.round((bytesInUse / limit) * 100);
                
                const usage = {
                    bytesInUse,
                    limit,
                    percentUsed,
                    available: limit - bytesInUse
                };
                
                debug(`存储使用情况: ${bytesInUse} / ${limit} 字节 (${percentUsed}%)`);
                resolve(usage);
            });
        });
    }
    
    /**
     * 清理过期和不必要的存储
     * @returns {Promise<number>} - 清理的字节数
     */
    async function cleanupStorage() {
        try {
            debug('开始清理存储...');
            
            // 获取所有键
            const keys = await getAllKeys();
            let cleanedBytes = 0;
            
            // 遍历所有键，检查过期项
            for (const key of keys) {
                const fullKey = getFullKey(key);
                const item = await new Promise(resolve => {
                    chrome.storage[config.storageType].get(fullKey, result => resolve(result[fullKey]));
                });
                
                // 检查是否过期
                if (item && item._expires && item._expires < Date.now()) {
                    // 获取项目大小
                    const itemSize = await new Promise(resolve => {
                        chrome.storage[config.storageType].getBytesInUse(fullKey, size => resolve(size));
                    });
                    
                    // 删除过期项
                    await remove(key);
                    cleanedBytes += itemSize;
                    debug(`删除过期项: ${key}, 大小: ${itemSize} 字节`);
                }
            }
            
            // 如果清理的空间不足，考虑删除最旧的项
            if (cleanedBytes === 0) {
                // 获取所有项目及其创建时间
                const items = [];
                for (const key of keys) {
                    const fullKey = getFullKey(key);
                    const item = await new Promise(resolve => {
                        chrome.storage[config.storageType].get(fullKey, result => resolve(result[fullKey]));
                    });
                    
                    // 记录项目信息
                    items.push({
                        key,
                        created: item && item._created ? item._created : 0
                    });
                }
                
                // 按创建时间排序
                items.sort((a, b) => a.created - b.created);
                
                // 删除最旧的10%项目
                const itemsToDelete = Math.max(1, Math.ceil(items.length * 0.1));
                for (let i = 0; i < itemsToDelete && i < items.length; i++) {
                    const key = items[i].key;
                    const fullKey = getFullKey(key);
                    
                    // 获取项目大小
                    const itemSize = await new Promise(resolve => {
                        chrome.storage[config.storageType].getBytesInUse(fullKey, size => resolve(size));
                    });
                    
                    // 删除项目
                    await remove(key);
                    cleanedBytes += itemSize;
                    debug(`删除最旧项: ${key}, 大小: ${itemSize} 字节`);
                }
            }
            
            debug(`存储清理完成，释放了 ${cleanedBytes} 字节`);
            return cleanedBytes;
        } catch (error) {
            debug('清理存储时出错:', error);
            return 0;
        }
    }
    
    /**
     * 添加存储监听器
     * @param {Function} callback - 回调函数
     */
    function addListener(callback) {
        if (typeof callback === 'function') {
            listeners.add(callback);
            debug('添加存储监听器');
        }
    }
    
    /**
     * 移除存储监听器
     * @param {Function} callback - 回调函数
     */
    function removeListener(callback) {
        if (listeners.has(callback)) {
            listeners.delete(callback);
            debug('移除存储监听器');
        }
    }
    
    /**
     * 通知所有监听器
     * @param {string} key - 变更的键
     * @param {*} value - 新值
     */
    function notifyListeners(key, value) {
        listeners.forEach(callback => {
            try {
                callback(key, value);
            } catch (error) {
                debug('调用监听器时出错:', error);
            }
        });
    }
    
    /**
     * 初始化存储管理器
     */
    function init() {
        debug('初始化存储管理器');
        
        // 监听存储变更事件
        chrome.storage.onChanged.addListener((changes, areaName) => {
            if (areaName !== config.storageType) return;
            
            // 处理变更
            Object.keys(changes).forEach(fullKey => {
                if (!fullKey.startsWith(config.prefix)) return;
                
                const key = fullKey.substring(config.prefix.length);
                const newValue = changes[fullKey].newValue;
                const oldValue = changes[fullKey].oldValue;
                
                // 提取实际值
                const actualNewValue = newValue && newValue._data !== undefined ? newValue._data : newValue;
                
                // 通知监听器
                notifyListeners(key, actualNewValue);
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('storage:changed', { 
                        key, 
                        newValue: actualNewValue, 
                        oldValue: oldValue && oldValue._data !== undefined ? oldValue._data : oldValue 
                    });
                }
            });
        });
        
        // 定期检查存储使用情况
        setInterval(async () => {
            try {
                const usage = await checkStorageUsage();
                
                // 如果使用率超过阈值，执行清理
                if (usage.percentUsed > config.cleanupThreshold) {
                    debug(`定期检查: 存储使用率(${usage.percentUsed}%)超过阈值(${config.cleanupThreshold}%)，执行清理`);
                    await cleanupStorage();
                }
            } catch (error) {
                debug('定期检查存储使用情况时出错:', error);
            }
        }, 30 * 60 * 1000); // 每30分钟检查一次
        
        // 初始化时执行一次存储清理
        cleanupStorage().catch(err => debug('初始化清理存储时出错:', err));
    }
    
    // 公开API
    return {
        get,
        set,
        remove,
        clear,
        getAllKeys,
        checkStorageUsage,
        cleanupStorage,
        addListener,
        removeListener,
        init
    };
})();

// 初始化存储管理器
if (typeof window !== 'undefined') {
    // 将存储管理器挂载到AIPDD.Core命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Core = window.AIPDD.Core || {};
    window.AIPDD.Core.Storage = window.AIPDD.StorageManager;
    
    // 初始化
    window.AIPDD.StorageManager.init();
    
    // 兼容旧版代码
    window.StateManager = {
        getState: window.AIPDD.StorageManager.get,
        setState: window.AIPDD.StorageManager.set,
        addListener: window.AIPDD.StorageManager.addListener
    };
}