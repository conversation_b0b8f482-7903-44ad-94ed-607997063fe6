/**
 * 通知管理器 - 负责处理所有通知相关功能
 */
(function(global) {
    'use strict';

    // 导入依赖
    const { debug } = global.AIPDD?.Core?.Utils?.Debug || { debug: console.log.bind(console, '[NotificationManager]') };
    const EventBus = global.AIPDD?.Core?.Utils?.EventBus;
    const Storage = global.AIPDD?.Core?.Storage;

    // 通知类型枚举
    const NotificationType = {
        INFO: 'info',           // 普通信息
        SUCCESS: 'success',     // 成功信息
        WARNING: 'warning',     // 警告信息
        ERROR: 'error',         // 错误信息
        SYSTEM: 'system',       // 系统通知
        PLATFORM: 'platform'    // 平台通知（如拼多多红点通知）
    };

    /**
     * 通知管理器类
     */
    class NotificationManager {
        /**
         * 构造函数
         */
        constructor() {
            // 初始化属性
            this.isInitialized = false;
            this.notifications = [];
            this.maxNotifications = 100;
            this.timers = {};
            this.settings = {};
            this.platformNotifiers = new Map();
            
            // 绑定方法
            this.init = this.init.bind(this);
            this.show = this.show.bind(this);
            this.clear = this.clear.bind(this);
            this.startPlatformNotifier = this.startPlatformNotifier.bind(this);
            this.stopPlatformNotifier = this.stopPlatformNotifier.bind(this);
            this.handlePlatformNotification = this.handlePlatformNotification.bind(this);
            this.loadSettings = this.loadSettings.bind(this);
            this.saveSettings = this.saveSettings.bind(this);
        }

        /**
         * 初始化通知管理器
         * @returns {Promise<boolean>} 是否初始化成功
         */
        async init() {
            if (this.isInitialized) {
                debug('通知管理器已初始化');
                return true;
            }

            debug('初始化通知管理器');

            try {
                // 加载设置
                await this.loadSettings();

                // 注册事件监听
                if (EventBus) {
                    EventBus.on('notification:show', this.show);
                    EventBus.on('notification:clear', this.clear);
                    EventBus.on('notification:start', this.startPlatformNotifier);
                    EventBus.on('notification:stop', this.stopPlatformNotifier);
                    EventBus.on('settings:updated', this.loadSettings);
                }

                // 注册平台通知检查器
                this.registerPlatformNotifiers();

                // 标记为已初始化
                this.isInitialized = true;

                // 如果设置为自动启动，则启动平台通知检查
                if (this.settings.autoStart) {
                    this.startAllPlatformNotifiers();
                }

                debug('通知管理器初始化完成');
                return true;
            } catch (error) {
                debug('初始化通知管理器失败:', error);
                return false;
            }
        }

        /**
         * 注册平台通知检查器
         * @private
         */
        registerPlatformNotifiers() {
            // 注册拼多多平台的通知检查器
            this.registerPlatformNotifier('pinduoduo', {
                name: '拼多多红点通知',
                description: '检查拼多多平台的红点通知并自动点击',
                enabled: true,
                interval: 1000,
                check: this.checkPinduoduoNotification.bind(this)
            });
        }

        /**
         * 注册平台通知检查器
         * @param {string} id - 检查器ID
         * @param {Object} notifier - 检查器配置
         * @returns {boolean} 是否注册成功
         */
        registerPlatformNotifier(id, notifier) {
            if (!id || !notifier || !notifier.check) {
                debug('注册平台通知检查器失败: 无效的参数');
                return false;
            }

            if (this.platformNotifiers.has(id)) {
                debug(`平台通知检查器 ${id} 已存在，更新检查器`);
                const existingNotifier = this.platformNotifiers.get(id);
                this.platformNotifiers.set(id, { ...existingNotifier, ...notifier });
            } else {
                debug(`注册新平台通知检查器: ${id}`);
                this.platformNotifiers.set(id, {
                    id,
                    name: notifier.name || id,
                    description: notifier.description || '',
                    enabled: notifier.enabled !== undefined ? notifier.enabled : true,
                    interval: notifier.interval || 1000,
                    check: notifier.check,
                    timer: null
                });
            }

            return true;
        }

        /**
         * 启动所有平台通知检查器
         * @returns {Promise<boolean>} 是否启动成功
         */
        async startAllPlatformNotifiers() {
            debug('启动所有平台通知检查器');
            
            let success = true;
            for (const [id, notifier] of this.platformNotifiers.entries()) {
                if (notifier.enabled) {
                    const result = await this.startPlatformNotifier(id);
                    if (!result) {
                        success = false;
                    }
                }
            }
            
            return success;
        }

        /**
         * 停止所有平台通知检查器
         * @returns {Promise<boolean>} 是否停止成功
         */
        async stopAllPlatformNotifiers() {
            debug('停止所有平台通知检查器');
            
            let success = true;
            for (const [id] of this.platformNotifiers.entries()) {
                const result = await this.stopPlatformNotifier(id);
                if (!result) {
                    success = false;
                }
            }
            
            return success;
        }

        /**
         * 启动平台通知检查器
         * @param {string} id - 检查器ID
         * @returns {Promise<boolean>} 是否启动成功
         */
        async startPlatformNotifier(id) {
            if (!this.platformNotifiers.has(id)) {
                debug(`平台通知检查器 ${id} 不存在`);
                return false;
            }

            const notifier = this.platformNotifiers.get(id);
            
            // 如果检查器已经在运行，则不重复启动
            if (notifier.timer) {
                debug(`平台通知检查器 ${id} 已经在运行`);
                return true;
            }

            // 如果检查器未启用，则不启动
            if (!notifier.enabled) {
                debug(`平台通知检查器 ${id} 未启用，不启动`);
                return false;
            }

            debug(`启动平台通知检查器: ${id}`);
            
            try {
                // 设置定时器
                notifier.timer = setInterval(async () => {
                    try {
                        await notifier.check();
                    } catch (error) {
                        debug(`平台通知检查器 ${id} 执行出错:`, error);
                    }
                }, notifier.interval);
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('notification:notifierStarted', { id, notifier });
                }
                
                return true;
            } catch (error) {
                debug(`启动平台通知检查器 ${id} 失败:`, error);
                return false;
            }
        }

        /**
         * 停止平台通知检查器
         * @param {string} id - 检查器ID
         * @returns {Promise<boolean>} 是否停止成功
         */
        async stopPlatformNotifier(id) {
            if (!this.platformNotifiers.has(id)) {
                debug(`平台通知检查器 ${id} 不存在`);
                return false;
            }

            const notifier = this.platformNotifiers.get(id);
            
            // 如果检查器未在运行，则不需要停止
            if (!notifier.timer) {
                debug(`平台通知检查器 ${id} 未在运行`);
                return true;
            }

            debug(`停止平台通知检查器: ${id}`);
            
            try {
                // 清除定时器
                clearInterval(notifier.timer);
                notifier.timer = null;
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('notification:notifierStopped', { id, notifier });
                }
                
                return true;
            } catch (error) {
                debug(`停止平台通知检查器 ${id} 失败:`, error);
                return false;
            }
        }

        /**
         * 检查拼多多红点通知
         * @private
         * @returns {Promise<boolean>} 是否有通知
         */
        async checkPinduoduoNotification() {
            try {
                const notificationElement = document.evaluate(
                    '//div/div[1]/ul/ul/li//i',
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                ).singleNodeValue;

                if (notificationElement) {
                    const parentElement = notificationElement.closest('div');
                    if (parentElement) {
                        // 点击通知
                        parentElement.click();
                        
                        // 触发通知事件
                        this.handlePlatformNotification('pinduoduo', {
                            type: NotificationType.PLATFORM,
                            title: '拼多多通知',
                            message: '检测到红点通知并自动点击',
                            timestamp: Date.now()
                        });
                        
                        return true;
                    }
                }
                
                return false;
            } catch (error) {
                debug('检查拼多多红点通知失败:', error);
                return false;
            }
        }

        /**
         * 处理平台通知
         * @param {string} platform - 平台ID
         * @param {Object} notification - 通知对象
         * @returns {Promise<boolean>} 是否处理成功
         */
        async handlePlatformNotification(platform, notification) {
            try {
                // 添加到通知列表
                this.addNotification({
                    ...notification,
                    platform,
                    handled: true
                });
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('notification:platformNotification', { platform, notification });
                }
                
                return true;
            } catch (error) {
                debug('处理平台通知失败:', error);
                return false;
            }
        }

        /**
         * 显示通知
         * @param {Object} options - 通知选项
         * @param {string} options.type - 通知类型
         * @param {string} options.title - 通知标题
         * @param {string} options.message - 通知内容
         * @param {number} options.duration - 通知持续时间（毫秒），0表示不自动关闭
         * @param {Function} options.onClick - 点击通知的回调函数
         * @returns {string} 通知ID
         */
        show(options = {}) {
            const notification = {
                id: `notification_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                type: options.type || NotificationType.INFO,
                title: options.title || '',
                message: options.message || '',
                timestamp: Date.now(),
                duration: options.duration !== undefined ? options.duration : 3000,
                onClick: options.onClick,
                handled: false
            };
            
            // 添加到通知列表
            this.addNotification(notification);
            
            // 如果设置了持续时间，则设置自动关闭定时器
            if (notification.duration > 0) {
                this.timers[notification.id] = setTimeout(() => {
                    this.clear(notification.id);
                }, notification.duration);
            }
            
            // 创建通知UI
            this.createNotificationUI(notification);
            
            return notification.id;
        }

        /**
         * 清除通知
         * @param {string} id - 通知ID，如果不指定则清除所有通知
         * @returns {boolean} 是否清除成功
         */
        clear(id) {
            if (!id) {
                // 清除所有通知
                for (const notification of this.notifications) {
                    this.removeNotificationUI(notification.id);
                    if (this.timers[notification.id]) {
                        clearTimeout(this.timers[notification.id]);
                        delete this.timers[notification.id];
                    }
                }
                
                this.notifications = [];
                return true;
            }
            
            // 清除指定通知
            const index = this.notifications.findIndex(n => n.id === id);
            if (index !== -1) {
                this.removeNotificationUI(id);
                if (this.timers[id]) {
                    clearTimeout(this.timers[id]);
                    delete this.timers[id];
                }
                
                this.notifications.splice(index, 1);
                return true;
            }
            
            return false;
        }

        /**
         * 添加通知到列表
         * @param {Object} notification - 通知对象
         * @private
         */
        addNotification(notification) {
            this.notifications.push(notification);
            
            // 如果通知数量超过最大值，则移除最旧的通知
            if (this.notifications.length > this.maxNotifications) {
                const oldestNotification = this.notifications.shift();
                this.removeNotificationUI(oldestNotification.id);
                if (this.timers[oldestNotification.id]) {
                    clearTimeout(this.timers[oldestNotification.id]);
                    delete this.timers[oldestNotification.id];
                }
            }
            
            // 触发事件
            if (EventBus) {
                EventBus.emit('notification:added', { notification });
            }
        }

        /**
         * 创建通知UI
         * @param {Object} notification - 通知对象
         * @private
         */
        createNotificationUI(notification) {
            // 如果通知已处理，则不创建UI
            if (notification.handled) {
                return;
            }
            
            // 检查是否存在通知容器，如果不存在则创建
            let notificationContainer = document.getElementById('aipdd-notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'aipdd-notification-container';
                notificationContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    gap: 10px;
                    max-width: 350px;
                `;
                document.body.appendChild(notificationContainer);
            }
            
            // 创建通知元素
            const notificationElement = document.createElement('div');
            notificationElement.id = notification.id;
            notificationElement.className = `aipdd-notification aipdd-notification-${notification.type}`;
            notificationElement.style.cssText = `
                background-color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                padding: 12px 16px;
                margin-bottom: 10px;
                min-width: 250px;
                max-width: 350px;
                animation: notification-slide-in 0.3s ease;
                position: relative;
                border-left: 4px solid #1890ff;
                transition: all 0.3s;
            `;
            
            // 根据通知类型设置边框颜色
            switch (notification.type) {
                case NotificationType.SUCCESS:
                    notificationElement.style.borderLeftColor = '#52c41a';
                    break;
                case NotificationType.WARNING:
                    notificationElement.style.borderLeftColor = '#faad14';
                    break;
                case NotificationType.ERROR:
                    notificationElement.style.borderLeftColor = '#f5222d';
                    break;
                case NotificationType.SYSTEM:
                    notificationElement.style.borderLeftColor = '#722ed1';
                    break;
                default:
                    notificationElement.style.borderLeftColor = '#1890ff';
            }
            
            // 创建通知内容
            const content = document.createElement('div');
            content.className = 'aipdd-notification-content';
            
            // 创建标题（如果有）
            if (notification.title) {
                const title = document.createElement('div');
                title.className = 'aipdd-notification-title';
                title.textContent = notification.title;
                title.style.cssText = `
                    font-weight: bold;
                    margin-bottom: 4px;
                    font-size: 14px;
                `;
                content.appendChild(title);
            }
            
            // 创建消息
            const message = document.createElement('div');
            message.className = 'aipdd-notification-message';
            message.textContent = notification.message;
            message.style.cssText = `
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
            `;
            content.appendChild(message);
            
            // 创建关闭按钮
            const closeButton = document.createElement('span');
            closeButton.className = 'aipdd-notification-close';
            closeButton.innerHTML = '×';
            closeButton.style.cssText = `
                position: absolute;
                top: 8px;
                right: 8px;
                font-size: 14px;
                cursor: pointer;
                color: rgba(0, 0, 0, 0.45);
            `;
            closeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.clear(notification.id);
            });
            
            // 添加点击事件（如果有）
            if (typeof notification.onClick === 'function') {
                notificationElement.style.cursor = 'pointer';
                notificationElement.addEventListener('click', () => {
                    notification.onClick(notification);
                });
            }
            
            // 组装通知元素
            notificationElement.appendChild(content);
            notificationElement.appendChild(closeButton);
            
            // 添加到容器
            notificationContainer.appendChild(notificationElement);
            
            // 添加淡出动画
            if (notification.duration > 0) {
                setTimeout(() => {
                    notificationElement.style.opacity = '0';
                }, notification.duration - 300);
            }
        }

        /**
         * 移除通知UI
         * @param {string} id - 通知ID
         * @private
         */
        removeNotificationUI(id) {
            const notificationElement = document.getElementById(id);
            if (notificationElement) {
                // 添加淡出动画
                notificationElement.style.opacity = '0';
                notificationElement.style.transform = 'translateX(100%)';
                
                // 动画结束后移除元素
                setTimeout(() => {
                    if (notificationElement.parentNode) {
                        notificationElement.parentNode.removeChild(notificationElement);
                    }
                    
                    // 如果容器中没有通知了，则移除容器
                    const notificationContainer = document.getElementById('aipdd-notification-container');
                    if (notificationContainer && !notificationContainer.hasChildNodes()) {
                        notificationContainer.parentNode.removeChild(notificationContainer);
                    }
                }, 300);
            }
        }

        /**
         * 加载通知设置
         * @returns {Promise<Object>} 设置对象
         */
        async loadSettings() {
            try {
                if (!Storage) {
                    debug('存储模块未加载，使用默认设置');
                    this.settings = {
                        autoStart: true,
                        maxNotifications: 100,
                        notifiers: {}
                    };
                    return this.settings;
                }
                
                // 从存储中加载设置
                const settings = await Storage.get('notificationSettings', {
                    autoStart: true,
                    maxNotifications: 100,
                    notifiers: {}
                });
                
                this.settings = settings;
                this.maxNotifications = settings.maxNotifications;
                
                debug('加载通知设置:', this.settings);
                
                // 更新平台通知检查器启用状态
                for (const [id, notifier] of this.platformNotifiers.entries()) {
                    const notifierSettings = settings.notifiers[id];
                    if (notifierSettings) {
                        notifier.enabled = notifierSettings.enabled !== undefined ? notifierSettings.enabled : notifier.enabled;
                        notifier.interval = notifierSettings.interval || notifier.interval;
                    }
                }
                
                return this.settings;
            } catch (error) {
                debug('加载通知设置失败:', error);
                return {};
            }
        }

        /**
         * 保存通知设置
         * @param {Object} settings - 设置对象
         * @returns {Promise<boolean>} 是否保存成功
         */
        async saveSettings(settings) {
            try {
                if (!Storage) {
                    debug('存储模块未加载，无法保存设置');
                    return false;
                }
                
                // 合并设置
                this.settings = { ...this.settings, ...settings };
                
                // 更新最大通知数
                if (settings.maxNotifications) {
                    this.maxNotifications = settings.maxNotifications;
                }
                
                // 更新平台通知检查器设置
                if (settings.notifiers) {
                    for (const [id, notifierSettings] of Object.entries(settings.notifiers)) {
                        if (this.platformNotifiers.has(id)) {
                            const notifier = this.platformNotifiers.get(id);
                            
                            // 更新启用状态
                            if (notifierSettings.enabled !== undefined && notifier.enabled !== notifierSettings.enabled) {
                                notifier.enabled = notifierSettings.enabled;
                                
                                // 根据新状态启动或停止检查器
                                if (notifierSettings.enabled) {
                                    await this.startPlatformNotifier(id);
                                } else {
                                    await this.stopPlatformNotifier(id);
                                }
                            }
                            
                            // 更新间隔时间
                            if (notifierSettings.interval && notifier.interval !== notifierSettings.interval) {
                                notifier.interval = notifierSettings.interval;
                                
                                // 如果检查器正在运行，则重启
                                if (notifier.timer) {
                                    await this.stopPlatformNotifier(id);
                                    await this.startPlatformNotifier(id);
                                }
                            }
                        }
                    }
                }
                
                // 保存到存储
                await Storage.set('notificationSettings', this.settings);
                debug('保存通知设置:', this.settings);
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('notification:settingsUpdated', { settings: this.settings });
                }
                
                return true;
            } catch (error) {
                debug('保存通知设置失败:', error);
                return false;
            }
        }
    }

    // 创建单例实例
    const notificationManager = new NotificationManager();

    // 导出模块
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Core) global.AIPDD.Core = {};
    if (!global.AIPDD.Core.Notification) global.AIPDD.Core.Notification = {};
    
    global.AIPDD.Core.Notification.NotificationManager = notificationManager;
    global.AIPDD.Core.Notification.NotificationType = NotificationType;

})(window); 