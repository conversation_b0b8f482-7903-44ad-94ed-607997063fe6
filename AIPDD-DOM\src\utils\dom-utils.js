// DOM操作工具库
window.AIPDD = window.AIPDD || {};
window.AIPDD.Utils = window.AIPDD.Utils || {};

(function() {
  // 获取Logger，如果已加载
  const Logger = window.AIPDD.Core?.Utils?.Logger || {
    debug: console.debug.bind(console),
    error: console.error.bind(console)
  };
  
  /**
   * 获取元素祖先信息
   * @param {Element} element - DOM元素
   * @param {number} maxDepth - 最大深度
   * @returns {Array<Object>} 祖先信息数组
   */
  function getAncestorInfo(element, maxDepth = 5) {
    if (!element) return [];
    
    const ancestors = [];
    let currentElement = element;
    let depth = 0;
    
    while (currentElement && depth < maxDepth) {
      const classes = Array.from(currentElement.classList || []);
      const id = currentElement.id;
      const tagName = currentElement.tagName?.toLowerCase();
      
      ancestors.push({
        tagName,
        id: id || null,
        classes: classes.length > 0 ? classes : null
      });
      
      currentElement = currentElement.parentElement;
      depth++;
    }
    
    return ancestors;
  }
  
  /**
   * 创建浮动窗口
   * @param {string} title - 窗口标题
   * @param {string|Element} content - 窗口内容
   * @param {Object} options - 选项
   * @returns {Element} 窗口元素
   */
  function createFloatingWindow(title, content, options = {}) {
    const {
      width = '400px',
      height = 'auto',
      draggable = true,
      closeButton = true,
      zIndex = 9999,
      onClose = null
    } = options;
    
    // 创建窗口容器
    const modal = document.createElement('div');
    modal.className = 'aipdd-floating-window';
    modal.style.cssText = `
      position: fixed;
      top: 100px;
      left: calc(50% - ${parseInt(width) / 2}px);
      width: ${width};
      height: ${height};
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: ${zIndex};
      display: flex;
      flex-direction: column;
      overflow: hidden;
    `;
    
    // 创建窗口头部
    const header = document.createElement('div');
    header.className = 'aipdd-floating-window-header';
    header.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #f9f9f9;
      cursor: ${draggable ? 'move' : 'default'};
    `;
    
    // 标题
    const titleElement = document.createElement('div');
    titleElement.textContent = title;
    titleElement.style.fontWeight = 'bold';
    header.appendChild(titleElement);
    
    // 关闭按钮
    if (closeButton) {
      const closeBtn = document.createElement('button');
      closeBtn.innerHTML = '✖';
      closeBtn.style.cssText = `
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
        color: #999;
      `;
      closeBtn.onclick = () => {
        document.body.removeChild(modal);
        if (typeof onClose === 'function') {
          onClose();
        }
      };
      header.appendChild(closeBtn);
    }
    
    // 内容区域
    const body = document.createElement('div');
    body.className = 'aipdd-floating-window-body';
    body.style.cssText = `
      padding: 16px;
      overflow-y: auto;
      flex: 1;
    `;
    
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else {
      body.appendChild(content);
    }
    
    // 组装窗口
    modal.appendChild(header);
    modal.appendChild(body);
    document.body.appendChild(modal);
    
    // 实现拖拽功能
    if (draggable) {
      let isDragging = false;
      let offsetX, offsetY;
      
      header.onmousedown = (e) => {
        isDragging = true;
        offsetX = e.clientX - modal.offsetLeft;
        offsetY = e.clientY - modal.offsetTop;
        
        // 防止选中文本
        e.preventDefault();
      };
      
      document.onmousemove = (e) => {
        if (!isDragging) return;
        
        const left = e.clientX - offsetX;
        const top = e.clientY - offsetY;
        
        modal.style.left = `${left}px`;
        modal.style.top = `${top}px`;
      };
      
      document.onmouseup = () => {
        isDragging = false;
      };
    }
    
    return modal;
  }
  
  /**
   * 添加样式到页面
   * @param {string} css - CSS内容
   * @param {string} id - 样式元素ID
   * @returns {Element} 样式元素
   */
  function addStyles(css, id = 'aipdd-styles') {
    // 检查是否已存在
    let styleElement = document.getElementById(id);
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = id;
      document.head.appendChild(styleElement);
    }
    
    styleElement.textContent = css;
    return styleElement;
  }
  
  /**
   * 安全地设置DOM元素内容
   * @param {Element} element - DOM元素
   * @param {string} html - HTML内容
   */
  function setInnerHTML(element, html) {
    if (!element) return;
    
    try {
      // 使用DOMParser创建一个安全的文档片段
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // 清空目标元素
      element.innerHTML = '';
      
      // 将解析后的内容复制到目标元素
      Array.from(doc.body.childNodes).forEach(node => {
        element.appendChild(node.cloneNode(true));
      });
    } catch (error) {
      Logger.error('DomUtils', '设置HTML内容失败:', error);
      // 降级处理
      element.textContent = html;
    }
  }
  
  /**
   * 创建并显示一个简单的提示信息
   * @param {string} message - 提示信息
   * @param {Object} options - 选项
   */
  function showToast(message, options = {}) {
    const {
      type = 'info',
      duration = 3000,
      position = 'top-right',
      zIndex = 10000
    } = options;
    
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `aipdd-toast aipdd-toast-${type}`;
    
    // 设置样式
    let positionStyle = '';
    switch (position) {
      case 'top-left':
        positionStyle = 'top: 20px; left: 20px;';
        break;
      case 'top-center':
        positionStyle = 'top: 20px; left: 50%; transform: translateX(-50%);';
        break;
      case 'bottom-left':
        positionStyle = 'bottom: 20px; left: 20px;';
        break;
      case 'bottom-center':
        positionStyle = 'bottom: 20px; left: 50%; transform: translateX(-50%);';
        break;
      case 'bottom-right':
        positionStyle = 'bottom: 20px; right: 20px;';
        break;
      case 'top-right':
      default:
        positionStyle = 'top: 20px; right: 20px;';
    }
    
    toast.style.cssText = `
      position: fixed;
      ${positionStyle}
      padding: 12px 16px;
      background-color: ${type === 'error' ? '#ffebee' : type === 'warning' ? '#fff8e1' : type === 'success' ? '#e8f5e9' : '#e3f2fd'};
      color: ${type === 'error' ? '#d32f2f' : type === 'warning' ? '#ff8f00' : type === 'success' ? '#388e3c' : '#1976d2'};
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: ${zIndex};
      max-width: 300px;
      word-break: break-word;
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
    `;
    
    // 设置消息内容
    toast.textContent = message;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 显示提示
    setTimeout(() => {
      toast.style.opacity = '1';
    }, 10);
    
    // 设置自动消失
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        if (toast.parentNode) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, duration);
    
    return toast;
  }
  
  // 导出到命名空间
  window.AIPDD.Utils.DomUtils = {
    getAncestorInfo,
    createFloatingWindow,
    addStyles,
    setInnerHTML,
    showToast
  };
  
  // 为了向后兼容，暴露部分函数到全局
  window.getAncestorInfo = getAncestorInfo;
  window.createFloatingWindow = createFloatingWindow;
  window.addStyles = addStyles;
})(); 