# AIPDD-DOM 项目重构文档

## 项目简介

AIPDD-DOM是一个基于AI的客服助手Chrome扩展，专为拼多多商家后台聊天界面设计。该扩展通过AI技术辅助商家客服人员提高工作效率，实现智能回复、自动转接等功能。

## 重构目标

本次重构旨在优化代码结构，提高可维护性和扩展性，解决原有代码中存在的问题：

- 代码高度耦合
- 功能分散，难以维护
- 缺乏统一的命名规范和架构设计
- 难以扩展支持其他平台

## 已完成的重构工作

### 第一阶段：基础架构搭建

1. **创建了基础目录结构**
   - 建立了模块化的目录结构，包括核心、平台、服务、UI和工具等模块

2. **开发了核心工具类**
   - `debug.js`：统一日志记录系统，支持多级日志
   - `event-bus.js`：事件总线，实现模块间松耦合通信
   - `storage-manager.js`：封装Chrome存储API
   - `constants.js`：集中管理系统常量
   - `common-utils.js`和`dom-utils.js`：通用工具函数库

3. **实现了平台抽象层**
   - `platform-interface.js`：定义平台接口规范
   - `pinduoduo-selectors.js`：集中管理拼多多平台的DOM选择器
   - `pinduoduo-platform.js`：拼多多平台实现类

4. **创建了主入口文件**
   - `main.js`用于加载和初始化所有模块

5. **更新了manifest.json**
   - 添加了新模块的访问权限
   - 更新版本号到1.5.0

## 接下来的重构计划

### 第二阶段：模块拆分和功能迁移

1. **拆分content.js**
   - 将大型content.js文件按功能拆分为多个模块
   - 确保每个模块都能独立工作且互相协作
   - 保持完整功能，不遗漏任何现有特性

2. **功能模块化实现**
   - **消息处理模块**
     - 提取`processMessage`、`determineMessageType`等函数
     - 实现消息解析、分类和处理逻辑
     - 保持消息处理流程的完整性
   
   - **UI模块**
     - 迁移`FloatingBall`类及其所有方法
     - 将所有弹窗、设置界面相关代码迁移到UI模块
     - 确保样式文件与UI组件一一对应
     - 将CSS从content.css拆分到对应组件的样式文件中

   - **自动回复模块**
     - 迁移`difyReplyProcess`及相关函数
     - 实现AI回复生成和处理逻辑
     - 保留所有回复类型的处理能力（文本、产品、图片等）

   - **转接模块**
     - 迁移`handleTransfer`及相关函数
     - 实现完整的转接流程和状态管理
     - 保留所有转接相关的功能和判断逻辑

   - **状态管理模块**
     - 迁移`ConversationState`类及相关状态管理函数
     - 实现统一的状态管理机制
     - 确保各模块间状态同步正确

3. **样式文件拆分**
   - 将content.css按功能模块拆分
   - 创建组件级别的CSS文件
   - 确保样式隔离，避免冲突
   - 实现样式按需加载机制

4. **集成Dify API服务**
   - 重构AI请求相关代码
   - 优化错误处理和响应机制
   - 确保API调用的可靠性和稳定性

### 第三阶段：优化和测试

1. **性能优化**
   - 减少DOM操作，优化选择器
   - 优化事件监听，减少重复绑定
   - 改进存储管理，减少不必要的存储操作
   - 实现资源按需加载，减少初始加载时间

2. **全面测试**
   - 单元测试：测试各个模块的独立功能
   - 集成测试：测试模块间的协作
   - 兼容性测试：确保在不同Chrome版本中正常工作
   - 功能回归测试：确保所有原有功能正常工作

3. **文档完善**
   - 更新使用文档
   - 完善开发文档
   - API接口文档
   - 模块依赖关系文档

### 第四阶段：持续改进和扩展

1. **代码质量提升**
   - 代码审查和重构
   - 统一编码风格
   - 增加注释和文档字符串

2. **功能扩展**
   - 支持更多平台
   - 增强AI能力
   - 优化用户体验

## 重构原则和注意事项

1. **渐进式重构**
   - 保持功能的持续可用
   - 分阶段实施，每个阶段都能正常工作
   - 每次迁移一个功能模块，确保其完整性

2. **技术约束**
   - 不支持ES模块导出语法（export）
   - 使用命名空间模式（`window.AIPDD`）和立即执行函数表达式（IIFE）
   - 通过函数和对象拆分模拟模块化
   - 使用动态脚本加载实现模块依赖

3. **代码规范**
   - 统一的命名规范
   - 完整的注释
   - 适当的日志记录

4. **功能与样式迁移原则**
   - 确保功能完整性：每个被拆分的功能必须保持原有的全部特性
   - 样式一致性：UI外观和交互体验不变
   - 模块间协作：通过事件总线实现模块间通信
   - 渐进式替换：先并行运行新旧代码，确认无问题后再移除旧代码

## 项目结构

```
AIPDD-DOM/
├── icons/                  # 扩展图标
├── images/                 # 图片资源
├── manifest.json          # 扩展配置文件
└── src/
    ├── background/         # 后台脚本
    │   └── background.js
    ├── config/             # 配置文件
    │   └── constants.js    # 常量定义
    ├── content/            # 内容脚本
    │   ├── content.css     # 主样式文件（将逐步拆分）
    │   ├── content.js      # 主要内容脚本（将逐步拆分）
    │   └── legacy/         # 原始脚本备份
    │       └── content-original.js
    ├── core/               # 核心模块
    │   ├── automation/     # 自动化功能
    │   ├── message/        # 消息处理
    │   │   ├── message-handler.js
    │   │   ├── message-parser.js
    │   │   └── message-types.js
    │   ├── storage/        # 存储管理
    │   │   └── storage-manager.js
    │   ├── transfer/       # 转接逻辑
    │   │   ├── transfer-handler.js
    │   │   └── transfer-state.js
    │   └── utils/          # 核心工具
    │       ├── debug.js    # 调试工具
    │       └── event-bus.js # 事件总线
    ├── main.js             # 主入口文件
    ├── modules/            # 功能模块
    │   ├── dify_api.js     # Dify API集成
    │   └── reply.js        # 回复处理
    ├── pages/              # HTML页面
    │   ├── api-monitor.html
    │   └── redirect.html
    ├── platforms/          # 平台适配层
    │   ├── pinduoduo/      # 拼多多平台实现
    │   │   ├── pinduoduo-platform.js
    │   │   └── pinduoduo-selectors.js
    │   └── platform-interface.js # 平台接口定义
    ├── popup/              # 弹出窗口
    │   ├── popup.html
    │   └── popup.js
    ├── services/           # 服务层
    │   ├── ai-service.js   # AI服务
    │   └── notification-service.js # 通知服务
    ├── styles/             # 样式文件
    │   ├── components/     # 组件样式
    │   │   ├── floating-ball.css
    │   │   ├── settings-dialog.css
    │   │   └── message-highlight.css
    │   └── shared/         # 共享样式
    │       └── variables.css
    ├── ui/                 # UI组件
    │   ├── floating-ball.js
    │   ├── settings-dialog.js
    │   └── message-ui.js
    └── utils/              # 通用工具
        ├── common-utils.js # 通用工具函数
        ├── common.js
        ├── dom-utils.js    # DOM操作工具
        ├── handleDifyResponse.js
        └── MessageBoxHandler.js
```

## 当前进度

- [x] 基础架构搭建
- [ ] content.js拆分
  - [ ] 消息处理模块
  - [ ] UI模块
  - [ ] 自动回复模块
  - [ ] 转接模块
  - [ ] 状态管理模块
- [ ] 样式文件拆分
  - [ ] 组件样式分离
  - [ ] 共享样式提取
- [ ] 功能模块迁移
  - [ ] 消息处理功能
  - [ ] UI功能
  - [ ] 自动回复功能
  - [ ] 转接功能
  - [ ] 状态管理功能
- [ ] Dify API服务集成
- [ ] 性能优化
- [ ] 测试验证
- [ ] 文档完善 