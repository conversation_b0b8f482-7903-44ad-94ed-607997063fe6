/**
 * 日志模块测试用例
 * 测试日志模块的基本功能、日志级别和持久化
 */

// 测试配置
const TEST_CONFIG = {
  // 测试模块名称
  testModuleName: 'TestModule',
  
  // 测试日志消息
  testMessage: 'This is a test log message',
  
  // 测试对象
  testObject: { name: 'Test Object', value: 42, nested: { key: 'value' } },
  
  // 日志持久化延迟（毫秒）
  persistDelay: 100
};

/**
 * 延迟执行函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise<void>}
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试基本日志功能
 */
async function testBasicLogging() {
  console.log('开始测试基本日志功能...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 获取默认日志记录器
    const logger = Logger.getLogger();
    
    // 备份原始控制台方法
    const originalConsoleLog = console.log;
    const originalConsoleInfo = console.info;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    
    // 创建模拟控制台方法
    let logCalled = false;
    let infoCalled = false;
    let warnCalled = false;
    let errorCalled = false;
    
    console.log = (...args) => {
      logCalled = true;
      originalConsoleLog(...args);
    };
    
    console.info = (...args) => {
      infoCalled = true;
      originalConsoleInfo(...args);
    };
    
    console.warn = (...args) => {
      warnCalled = true;
      originalConsoleWarn(...args);
    };
    
    console.error = (...args) => {
      errorCalled = true;
      originalConsoleError(...args);
    };
    
    // 测试不同级别的日志
    logger.debug(TEST_CONFIG.testMessage);
    logger.info(TEST_CONFIG.testMessage);
    logger.warn(TEST_CONFIG.testMessage);
    logger.error(TEST_CONFIG.testMessage);
    
    // 恢复原始控制台方法
    console.log = originalConsoleLog;
    console.info = originalConsoleInfo;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
    
    // 验证日志方法是否被调用
    if (logCalled && infoCalled && warnCalled && errorCalled) {
      console.log('✓ 所有日志级别测试成功');
    } else {
      console.warn(`⚠️ 部分日志级别测试失败: log=${logCalled}, info=${infoCalled}, warn=${warnCalled}, error=${errorCalled}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 基本日志测试失败:', error);
    return false;
  }
}

/**
 * 测试模块化日志
 */
async function testModuleLogger() {
  console.log('开始测试模块化日志...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 获取模块日志记录器
    const moduleLogger = Logger.getLogger(TEST_CONFIG.testModuleName);
    
    // 备份原始控制台方法
    const originalConsoleLog = console.log;
    
    // 创建模拟控制台方法
    let loggedModule = null;
    
    console.log = (...args) => {
      // 检查日志中是否包含模块名称
      const logString = args.join(' ');
      if (logString.includes(TEST_CONFIG.testModuleName)) {
        loggedModule = TEST_CONFIG.testModuleName;
      }
      originalConsoleLog(...args);
    };
    
    // 测试模块日志
    moduleLogger.info(TEST_CONFIG.testMessage);
    
    // 恢复原始控制台方法
    console.log = originalConsoleLog;
    
    // 验证模块名称是否出现在日志中
    if (loggedModule === TEST_CONFIG.testModuleName) {
      console.log('✓ 模块化日志测试成功');
    } else {
      throw new Error('模块名称未出现在日志中');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 模块化日志测试失败:', error);
    return false;
  }
}

/**
 * 测试日志级别过滤
 */
async function testLogLevelFiltering() {
  console.log('开始测试日志级别过滤...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 获取模块日志记录器
    const moduleLogger = Logger.getLogger('LevelTest');
    
    // 备份原始控制台方法
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // 创建模拟控制台方法
    let debugCalled = false;
    let errorCalled = false;
    
    console.log = (...args) => {
      debugCalled = true;
      originalConsoleLog(...args);
    };
    
    console.error = (...args) => {
      errorCalled = true;
      originalConsoleError(...args);
    };
    
    // 保存原始日志级别
    const originalLevel = Logger.getLogLevel();
    
    // 设置日志级别为ERROR（只显示错误）
    Logger.setLogLevel(Logger.LEVELS.ERROR);
    
    // 测试不同级别的日志
    moduleLogger.debug('This should not appear');
    moduleLogger.error('This should appear');
    
    // 恢复原始控制台方法
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    
    // 恢复原始日志级别
    Logger.setLogLevel(originalLevel);
    
    // 验证日志级别过滤是否生效
    if (!debugCalled && errorCalled) {
      console.log('✓ 日志级别过滤测试成功');
    } else {
      throw new Error(`日志级别过滤测试失败: debug=${debugCalled}, error=${errorCalled}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 日志级别过滤测试失败:', error);
    return false;
  }
}

/**
 * 测试对象格式化
 */
async function testObjectFormatting() {
  console.log('开始测试对象格式化...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 获取日志记录器
    const logger = Logger.getLogger();
    
    // 备份原始控制台方法
    const originalConsoleLog = console.log;
    
    // 创建模拟控制台方法
    let formattedObject = null;
    
    console.log = (...args) => {
      // 检查日志中是否包含格式化的对象
      const logString = args.join(' ');
      if (logString.includes('"name":"Test Object"')) {
        formattedObject = TEST_CONFIG.testObject;
      }
      originalConsoleLog(...args);
    };
    
    // 测试对象日志
    logger.info('Object test:', TEST_CONFIG.testObject);
    
    // 恢复原始控制台方法
    console.log = originalConsoleLog;
    
    // 验证对象是否被正确格式化
    if (formattedObject) {
      console.log('✓ 对象格式化测试成功');
    } else {
      throw new Error('对象未被正确格式化');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 对象格式化测试失败:', error);
    return false;
  }
}

/**
 * 测试日志持久化
 */
async function testLogPersistence() {
  console.log('开始测试日志持久化...');
  
  try {
    const Logger = window.AIPDD.Logger;
    const Storage = window.AIPDD.Storage;
    
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    if (!Storage) {
      console.log('⚠️ 存储模块未加载，跳过持久化测试');
      return true;
    }
    
    // 启用日志持久化
    Logger.enablePersistence(true);
    
    // 清除现有持久化日志
    await Storage.remove('aipdd_logs');
    
    // 生成唯一的测试消息
    const uniqueMessage = `Test persistence ${Date.now()}`;
    
    // 记录日志
    Logger.getLogger().error(uniqueMessage);
    
    // 等待持久化完成
    await delay(TEST_CONFIG.persistDelay);
    
    // 获取持久化的日志
    const logs = await Storage.get('aipdd_logs', []);
    
    // 禁用日志持久化
    Logger.enablePersistence(false);
    
    // 验证日志是否被持久化
    const foundLog = logs.some(log => log.message && log.message.includes(uniqueMessage));
    
    if (foundLog) {
      console.log('✓ 日志持久化测试成功');
    } else {
      throw new Error('日志未被正确持久化');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 日志持久化测试失败:', error);
    return false;
  } finally {
    // 确保禁用持久化
    if (window.AIPDD?.Logger) {
      window.AIPDD.Logger.enablePersistence(false);
    }
  }
}

/**
 * 测试远程日志上报
 */
async function testRemoteLogging() {
  console.log('开始测试远程日志上报...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 备份原始fetch方法
    const originalFetch = window.fetch;
    
    // 创建模拟fetch方法
    let fetchCalled = false;
    let logData = null;
    
    window.fetch = (url, options) => {
      fetchCalled = true;
      
      // 解析请求体
      if (options && options.body) {
        try {
          logData = JSON.parse(options.body);
        } catch (e) {
          console.error('解析请求体失败:', e);
        }
      }
      
      // 返回成功的Promise
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });
    };
    
    // 配置远程日志
    const remoteConfig = {
      enabled: true,
      endpoint: 'https://example.com/logs',
      minLevel: Logger.LEVELS.ERROR
    };
    
    // 启用远程日志
    Logger.configureRemoteLogging(remoteConfig);
    
    // 生成唯一的测试消息
    const uniqueMessage = `Test remote ${Date.now()}`;
    
    // 记录错误日志（应该触发远程上报）
    Logger.getLogger().error(uniqueMessage);
    
    // 等待远程上报完成
    await delay(TEST_CONFIG.persistDelay);
    
    // 恢复原始fetch方法
    window.fetch = originalFetch;
    
    // 禁用远程日志
    Logger.configureRemoteLogging({ enabled: false });
    
    // 验证远程上报是否被调用
    if (fetchCalled) {
      console.log('✓ 远程日志上报测试成功');
      
      // 验证上报的数据是否包含测试消息
      if (logData && logData.message && logData.message.includes(uniqueMessage)) {
        console.log('✓ 远程日志数据验证成功');
      } else {
        console.warn('⚠️ 远程日志数据验证失败');
      }
    } else {
      throw new Error('远程日志上报未被调用');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 远程日志上报测试失败:', error);
    return false;
  } finally {
    // 确保禁用远程日志
    if (window.AIPDD?.Logger) {
      window.AIPDD.Logger.configureRemoteLogging({ enabled: false });
    }
  }
}

/**
 * 测试旧版API兼容性
 */
async function testLegacyAPICompatibility() {
  console.log('开始测试旧版API兼容性...');
  
  try {
    const Logger = window.AIPDD.Logger;
    if (!Logger) {
      throw new Error('日志模块未加载');
    }
    
    // 检查debug函数是否存在
    if (typeof window.debug !== 'function') {
      console.log('⚠️ 全局debug函数不存在，跳过兼容性测试');
      return true;
    }
    
    // 备份原始控制台方法
    const originalConsoleLog = console.log;
    
    // 创建模拟控制台方法
    let debugCalled = false;
    
    console.log = (...args) => {
      debugCalled = true;
      originalConsoleLog(...args);
    };
    
    // 使用旧版debug API
    window.debug('Legacy API test');
    
    // 恢复原始控制台方法
    console.log = originalConsoleLog;
    
    // 验证旧版API是否正常工作
    if (debugCalled) {
      console.log('✓ 旧版API兼容性测试成功');
    } else {
      throw new Error('旧版API未正常工作');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 旧版API兼容性测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('===== 日志模块测试开始 =====');
  
  let passedCount = 0;
  let failedCount = 0;
  
  // 测试基本日志功能
  if (await testBasicLogging()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试模块化日志
  if (await testModuleLogger()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试日志级别过滤
  if (await testLogLevelFiltering()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试对象格式化
  if (await testObjectFormatting()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试日志持久化
  if (await testLogPersistence()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试远程日志上报
  if (await testRemoteLogging()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试旧版API兼容性
  if (await testLegacyAPICompatibility()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n===== 日志模块测试结束 =====');
  console.log(`通过: ${passedCount} 测试`);
  console.log(`失败: ${failedCount} 测试`);
  
  return {
    total: passedCount + failedCount,
    passed: passedCount,
    failed: failedCount,
    success: failedCount === 0
  };
}

// 导出测试函数
window.LoggerModuleTest = {
  runAllTests,
  testBasicLogging,
  testModuleLogger,
  testLogLevelFiltering,
  testObjectFormatting,
  testLogPersistence,
  testRemoteLogging,
  testLegacyAPICompatibility
}; 