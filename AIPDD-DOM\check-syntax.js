// 语法检查脚本
const fs = require('fs');
const path = require('path');

// 要检查的文件列表
const filesToCheck = [
    'src/core/utils/debug.js',
    'src/core/utils/dom-utils.js',
    'src/core/settings/settings-manager.js',
    'src/core/storage/storage-manager.js',
    'src/core/transfer/transfer-rules.js',
    'src/core/transfer/transfer-manager.js',
    'src/core/transfer/transfer-loader.js',
    'src/core/ui/dialog.js',
    'src/content/content.js'
];

console.log('开始语法检查...\n');

let hasErrors = false;

filesToCheck.forEach(filePath => {
    console.log(`检查文件: ${filePath}`);
    
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`  ❌ 文件不存在`);
            hasErrors = true;
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 基本语法检查
        try {
            // 尝试解析JavaScript
            new Function(content);
            console.log(`  ✅ 语法正确`);
        } catch (syntaxError) {
            console.log(`  ❌ 语法错误: ${syntaxError.message}`);
            hasErrors = true;
        }
        
        // 检查常见问题
        const lines = content.split('\n');
        let openBraces = 0;
        let openParens = 0;
        let inString = false;
        let stringChar = '';
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                const prevChar = j > 0 ? line[j-1] : '';
                
                if (!inString) {
                    if (char === '"' || char === "'" || char === '`') {
                        inString = true;
                        stringChar = char;
                    } else if (char === '{') {
                        openBraces++;
                    } else if (char === '}') {
                        openBraces--;
                    } else if (char === '(') {
                        openParens++;
                    } else if (char === ')') {
                        openParens--;
                    }
                } else {
                    if (char === stringChar && prevChar !== '\\') {
                        inString = false;
                        stringChar = '';
                    }
                }
            }
        }
        
        if (openBraces !== 0) {
            console.log(`  ⚠️  大括号不匹配 (差值: ${openBraces})`);
        }
        
        if (openParens !== 0) {
            console.log(`  ⚠️  小括号不匹配 (差值: ${openParens})`);
        }
        
    } catch (error) {
        console.log(`  ❌ 读取文件失败: ${error.message}`);
        hasErrors = true;
    }
    
    console.log('');
});

if (hasErrors) {
    console.log('❌ 发现语法错误，请修复后重试');
    process.exit(1);
} else {
    console.log('✅ 所有文件语法检查通过');
    process.exit(0);
}
