/**
 * 消息解析器
 * 负责解析DOM元素中的消息内容，判断消息类型
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.MessageParser = (function() {
    'use strict';
    
    // 私有变量
    const debug = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.Debug ? 
        window.AIPDD.Utils.Debug.log : 
        console.log.bind(console, '[MessageParser]');
    
    // 引用消息类型常量
    const MESSAGE_TYPES = window.AIPDD.MessageTypes || {
        TEXT: 'text',
        IMAGE: 'image',
        PRODUCT: 'product',
        ORDER: 'order',
        REFUND: 'refund',
        LEGOCARD: 'legocard',
        UNKNOWN: 'unknown'
    };
    
    /**
     * 提取消息内容
     * @param {HTMLElement} element - 消息DOM元素
     * @returns {string} - 提取的消息内容
     */
    function extractMessageContent(element) {
        if (!element) return '';

        let content = '';

        // 获取文本内容
        const textElement = element.querySelector('.msg-content-box, .text-content');
        if (textElement) {
            content = textElement.textContent?.trim() || '';
            debug('提取到文本内容:', content);
        }

        // 检查商品卡
        const goodCard = element.querySelector('.msg-content.good-card');
        if (goodCard) {
            const productName = goodCard.querySelector('.good-name')?.textContent?.trim();
            const productPrice = goodCard.querySelector('.good-price')?.textContent?.trim();
            const productId = goodCard.querySelector('.good-id')?.textContent?.replace('商品ID：', '').replace('复制', '')?.trim();
            
            // 将商品信息作为文本内容
            content = `商品咨询：${productName}\n价格：${productPrice}\n商品ID：${productId}`;
            debug('提取到商品卡内容:', content);
        }

        // 检查 notify-card
        const notifyCard = element.querySelector('.notify-card');
        if (notifyCard) {
            const productName = notifyCard.querySelector('p')?.textContent?.trim();
            const productPrice = notifyCard.querySelector('span:last-child')?.textContent?.trim();
            const source = notifyCard.querySelector('.title span')?.textContent?.trim();
            
            if (content) {
                content += `\n\n[用户${source}]\n商品：${productName}\n价格：${productPrice}`;
            } else {
                content = `[用户${source}]\n商品：${productName}\n价格：${productPrice}`;
            }
            
            debug('合并商品卡信息后的内容:', content);
        }

        return content;
    }

    /**
     * 生成消息ID
     * @param {HTMLElement} element - 消息DOM元素
     * @returns {string} - 生成的消息ID
     */
    function generateMessageId(element) {
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 8);
        return `msg_${timestamp}_${randomStr}`;
    }

    /**
     * 获取商品卡片信息
     * @param {HTMLElement} element - 消息DOM元素
     * @returns {Object|boolean} - 商品信息对象或false
     */
    function getProductCardInfo(element) {
        try {
            // 商品详情页进入，用户主动发商品卡咨询
            // 检查 msg-content good-card 格式
            const goodCard = element.querySelector('.buyer-item .msg-content.good-card');
            if (goodCard) {
                const info = {
                    goodsId: goodCard.querySelector('.good-id')?.textContent?.replace('商品ID：', '')?.replace('复制','')?.trim(),
                    goodsName: goodCard.querySelector('.good-name')?.textContent?.trim(),
                    type: MESSAGE_TYPES.PRODUCT_CARD_TYPES.STANDARD
                };
                return info;
            }
            
            // 检查 notify-card 从商品详情页进入，默认的通知
            const goodContent = element.querySelector('.notify-card .good-content');
            if (goodContent) {
                //过滤售后方式的商品卡片
                if(element.querySelector('.notify-card .title')?.textContent?.includes('售后')) {
                    return false;
                }

                return {
                    goodsName: goodContent.querySelector('p')?.textContent?.trim(),
                    type: MESSAGE_TYPES.PRODUCT_CARD_TYPES.NOTIFY
                };
            }

            //匹配商品规格卡
            const productSpec = element.querySelector('.msg-content.lego-card');
            if (productSpec) {
                const skuCard = productSpec.querySelector('[class*="mallGoodsSkuCard"]');
                if(skuCard){
                    const goodsSku = productSpec.querySelector('[class*="goodsSku"]');
                    if(goodsSku){
                        //取数据
                        return {
                            goodsName: goodsSku.querySelector('[class*="goodsName"]')?.textContent,
                            skuName: goodsSku.querySelector('[class*="goodsSpec"]')?.textContent,
                            type: MESSAGE_TYPES.PRODUCT_CARD_TYPES.SPEC
                        };
                    }
                }
            }

            return false;
        } catch (error) {
            debug('获取商品信息异常:', error);
            return false;
        }
    }

    /**
     * 检查商品卡后跟文本的情况
     * @param {HTMLElement} element - 消息DOM元素
     * @returns {Object|boolean} - 商品信息对象或false
     */
    function checkProductAndText(element) {
        //上一个节点
        let before_dom = element.previousElementSibling;
        if(before_dom) {
            let res = getProductCardInfo(before_dom);
            if(res === false) {
                return false;
            } else {
                return res;
            }
        }
        return false;
    }

    /**
     * 检查文本后跟商品卡的情况
     * @param {HTMLElement} element - 消息DOM元素
     * @returns {string|boolean} - 文本内容或false
     */
    function checkTextAndProduct(element) {
        //上一个节点
        let before_dom = element.previousElementSibling;
        if(before_dom) {
            let res = before_dom.querySelector('.buyer-item');
            if(res) {
                return res?.textContent;
            }
        }
        return false;
    }

    /**
     * 判断消息类型
     * @param {Object} message - 消息对象
     * @returns {string} - 消息类型
     */
    function determineMessageType(message) {
        if (!message?.element) return MESSAGE_TYPES.UNKNOWN;
        
        debug('开始判断消息类型');
        
        // 先检查售后/退款类型 - 提高优先级，确保这些类型被优先识别
        if (message.element.querySelector('.refund-card, .apply-card')) {
            debug('检测到退款/售后申请消息');
            return MESSAGE_TYPES.REFUND;
        }
        
        // 检查commonCardTemp类型的售后卡片
        if (message.element.querySelector('.commonCardTemp') && 
            (message.element.querySelector('.commonCardTemp .title')?.textContent.includes('售后') ||
             message.element.querySelector('.commonCardTemp .text-content')?.textContent.includes('售后'))) {
            debug('检测到平台售后通知消息');
            return MESSAGE_TYPES.REFUND;
        }
        
        // 检查商品卡
        const goodCard = message.element.querySelector('.msg-content.good-card');
        if (goodCard) {
            debug('检测到商品卡消息');
            return MESSAGE_TYPES.PRODUCT;
        }
        
        // 检查文本内容
        if (message.content && typeof message.content === 'string' && message.content.trim()) {
            debug('检测到文本消息');
            return MESSAGE_TYPES.TEXT;
        }
        
        // 检查其他类型
        if (message.element.querySelector('.order-card')) {
            debug('检测到订单消息');
            return MESSAGE_TYPES.ORDER;
        }
        
        if (message.element.querySelector('.image-msg, .video-content') || message.content?.trim() === '[图片]') {
            debug('检测到图片消息');
            return MESSAGE_TYPES.IMAGE;
        }
        
        // 检查乐高卡片
        if (message.element.querySelector('.msg-content.lego-card')) {
            debug('检测到乐高卡片消息');
            return MESSAGE_TYPES.LEGOCARD;
        }
        
        debug('无法识别的消息类型，默认为未知类型');
        return MESSAGE_TYPES.UNKNOWN;
    }

    /**
     * 创建消息对象
     * @param {HTMLElement} element - 消息DOM元素
     * @param {string} conversationId - 会话ID
     * @returns {Object} - 消息对象
     */
    function createMessageObject(element, conversationId) {
        if (!element) return null;
        
        const messageObj = {
            element: element,
            id: generateMessageId(element),
            conversationId: conversationId,
            timestamp: Date.now(),
            content: extractMessageContent(element),
            productInfo: getProductCardInfo(element)
        };
        
        // 设置消息类型
        messageObj.type = determineMessageType(messageObj);
        
        return messageObj;
    }
    
    // 公开API
    return {
        extractMessageContent,
        generateMessageId,
        getProductCardInfo,
        checkProductAndText,
        checkTextAndProduct,
        determineMessageType,
        createMessageObject
    };
})();

// 兼容旧版代码
if (typeof window !== 'undefined') {
    // 将关键函数暴露到全局作用域，以便与现有代码兼容
    window.extractMessageContent = window.AIPDD.MessageParser.extractMessageContent;
    window.generateMessageId = window.AIPDD.MessageParser.generateMessageId;
    window.getProductCardInfo = window.AIPDD.MessageParser.getProductCardInfo;
    window.determineMessageType = window.AIPDD.MessageParser.determineMessageType;
}