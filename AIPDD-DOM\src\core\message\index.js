/**
 * 消息模块入口文件
 * 统一导出消息相关功能
 */

// 确保依赖模块已加载
(function() {
    'use strict';
    
    // 加载消息类型定义
    if (!window.AIPDD || !window.AIPDD.MessageTypes) {
        console.error('[Message] 消息类型定义未加载');
    }
    
    // 加载消息解析器
    if (!window.AIPDD || !window.AIPDD.MessageParser) {
        console.error('[Message] 消息解析器未加载');
    }
    
    // 加载消息处理器
    if (!window.AIPDD || !window.AIPDD.MessageHandler) {
        console.error('[Message] 消息处理器未加载');
    }
    
    // 创建命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Core = window.AIPDD.Core || {};
    window.AIPDD.Core.Message = {
        // 导出消息类型
        Types: window.AIPDD.MessageTypes,
        
        // 导出消息解析器
        Parser: window.AIPDD.MessageParser,
        
        // 导出消息处理器
        Handler: window.AIPDD.MessageHandler,
        
        // 便捷方法
        createMessage: function(element, conversationId) {
            return window.AIPDD.MessageParser.createMessageObject(element, conversationId);
        },
        
        processMessage: function(message, conversationId) {
            return window.AIPDD.MessageHandler.processMessage(message, conversationId);
        },
        
        markAsReplied: function(messageElement, conversationId) {
            return window.AIPDD.MessageHandler.markMessageAsReplied(messageElement, conversationId);
        },
        
        // 初始化方法
        init: function() {
            console.log('[Message] 消息模块初始化');
            
            // 如果有事件总线，注册事件
            if (window.AIPDD.Utils && window.AIPDD.Utils.EventBus) {
                const EventBus = window.AIPDD.Utils.EventBus;
                
                // 注册消息处理事件
                EventBus.on('message:new', function(data) {
                    const { element, conversationId } = data;
                    const message = window.AIPDD.MessageParser.createMessageObject(element, conversationId);
                    window.AIPDD.MessageHandler.processMessage(message, conversationId);
                });
            }
            
            return this;
        }
    };
    
    // 导出到全局命名空间
    window.AIPDD.Message = window.AIPDD.Core.Message;
    
    console.log('[Message] 消息模块加载完成');
})(); 