/**
 * 自动化管理器 - 负责管理所有自动化任务
 */
(function(global) {
    'use strict';

    // 导入依赖
    const { debug } = global.AIPDD?.Core?.Utils?.Debug || { debug: console.log.bind(console, '[AutomationManager]') };
    const EventBus = global.AIPDD?.Core?.Utils?.EventBus;
    const Storage = global.AIPDD?.Core?.Storage;
    const Message = global.AIPDD?.Core?.Message;
    const Transfer = global.AIPDD?.Core?.Transfer;

    // 自动化任务状态枚举
    const TaskState = {
        IDLE: 'idle',
        RUNNING: 'running',
        PAUSED: 'paused',
        COMPLETED: 'completed',
        FAILED: 'failed'
    };

    /**
     * 自动化管理器类
     */
    class AutomationManager {
        /**
         * 构造函数
         */
        constructor() {
            // 初始化属性
            this.isInitialized = false;
            this.isRunning = false;
            this.tasks = new Map();
            this.timers = new Map();
            this.settings = {};
            
            // 绑定方法
            this.init = this.init.bind(this);
            this.start = this.start.bind(this);
            this.stop = this.stop.bind(this);
            this.registerTask = this.registerTask.bind(this);
            this.unregisterTask = this.unregisterTask.bind(this);
            this.runTask = this.runTask.bind(this);
            this.stopTask = this.stopTask.bind(this);
            this.loadSettings = this.loadSettings.bind(this);
            this.saveSettings = this.saveSettings.bind(this);
        }

        /**
         * 初始化自动化管理器
         * @returns {Promise<boolean>} 是否初始化成功
         */
        async init() {
            if (this.isInitialized) {
                debug('自动化管理器已初始化');
                return true;
            }

            debug('初始化自动化管理器');

            try {
                // 加载设置
                await this.loadSettings();

                // 注册事件监听
                if (EventBus) {
                    EventBus.on('automation:start', this.start);
                    EventBus.on('automation:stop', this.stop);
                    EventBus.on('automation:registerTask', this.registerTask);
                    EventBus.on('automation:unregisterTask', this.registerTask);
                    EventBus.on('automation:runTask', this.runTask);
                    EventBus.on('automation:stopTask', this.stopTask);
                    EventBus.on('settings:updated', this.loadSettings);
                }

                // 注册默认任务
                this.registerTask('autoReply', {
                    name: '自动回复',
                    description: '自动回复客户消息',
                    enabled: this.settings.autoReplyEnabled || false,
                    interval: 1000,
                    run: this._runAutoReplyTask.bind(this)
                });

                this.registerTask('autoTransfer', {
                    name: '自动转人工',
                    description: '根据规则自动转接人工客服',
                    enabled: this.settings.autoTransferEnabled || false,
                    interval: 0, // 不需要定时执行，由消息处理触发
                    run: this._runAutoTransferTask.bind(this)
                });

                this.registerTask('checkUnread', {
                    name: '检查未读消息',
                    description: '定时检查未读消息并处理',
                    enabled: true,
                    interval: 1000,
                    run: this._runCheckUnreadTask.bind(this)
                });

                this.registerTask('healthCheck', {
                    name: '健康检查',
                    description: '定时检查系统健康状态',
                    enabled: true,
                    interval: 30000,
                    run: this._runHealthCheckTask.bind(this)
                });

                // 标记为已初始化
                this.isInitialized = true;

                // 如果设置为自动启动，则启动自动化
                if (this.settings.autoStart) {
                    await this.start();
                }

                debug('自动化管理器初始化完成');
                return true;
            } catch (error) {
                debug('初始化自动化管理器失败:', error);
                return false;
            }
        }

        /**
         * 启动自动化管理器
         * @returns {Promise<boolean>} 是否启动成功
         */
        async start() {
            if (!this.isInitialized) {
                await this.init();
            }

            if (this.isRunning) {
                debug('自动化管理器已经在运行');
                return true;
            }

            debug('启动自动化管理器');

            try {
                // 启动所有已启用的任务
                for (const [id, task] of this.tasks.entries()) {
                    if (task.enabled) {
                        await this.runTask(id);
                    }
                }

                this.isRunning = true;
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('automation:started', { manager: this });
                }

                debug('自动化管理器启动成功');
                return true;
            } catch (error) {
                debug('启动自动化管理器失败:', error);
                return false;
            }
        }

        /**
         * 停止自动化管理器
         * @returns {Promise<boolean>} 是否停止成功
         */
        async stop() {
            if (!this.isRunning) {
                debug('自动化管理器未在运行');
                return true;
            }

            debug('停止自动化管理器');

            try {
                // 停止所有任务
                for (const [id] of this.tasks.entries()) {
                    await this.stopTask(id);
                }

                this.isRunning = false;
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('automation:stopped', { manager: this });
                }

                debug('自动化管理器停止成功');
                return true;
            } catch (error) {
                debug('停止自动化管理器失败:', error);
                return false;
            }
        }

        /**
         * 注册自动化任务
         * @param {string} id - 任务ID
         * @param {Object} task - 任务配置
         * @returns {boolean} 是否注册成功
         */
        registerTask(id, task) {
            if (!id || !task) {
                debug('注册任务失败: 无效的参数');
                return false;
            }

            if (this.tasks.has(id)) {
                debug(`任务 ${id} 已存在，更新任务`);
                const existingTask = this.tasks.get(id);
                this.tasks.set(id, { ...existingTask, ...task });
            } else {
                debug(`注册新任务: ${id}`);
                this.tasks.set(id, {
                    id,
                    name: task.name || id,
                    description: task.description || '',
                    enabled: task.enabled !== undefined ? task.enabled : true,
                    interval: task.interval || 0,
                    state: TaskState.IDLE,
                    lastRun: null,
                    run: task.run || (() => Promise.resolve(true))
                });
            }

            // 如果管理器正在运行且任务已启用，则启动任务
            if (this.isRunning && task.enabled) {
                this.runTask(id);
            }

            return true;
        }

        /**
         * 注销自动化任务
         * @param {string} id - 任务ID
         * @returns {boolean} 是否注销成功
         */
        unregisterTask(id) {
            if (!this.tasks.has(id)) {
                debug(`任务 ${id} 不存在`);
                return false;
            }

            // 如果任务正在运行，先停止
            if (this.timers.has(id)) {
                this.stopTask(id);
            }

            // 从任务列表中移除
            this.tasks.delete(id);
            debug(`任务 ${id} 已注销`);

            return true;
        }

        /**
         * 运行自动化任务
         * @param {string} id - 任务ID
         * @returns {Promise<boolean>} 是否运行成功
         */
        async runTask(id) {
            if (!this.tasks.has(id)) {
                debug(`任务 ${id} 不存在`);
                return false;
            }

            const task = this.tasks.get(id);
            
            // 如果任务已经在运行，则不重复启动
            if (this.timers.has(id)) {
                debug(`任务 ${id} 已经在运行`);
                return true;
            }

            // 如果任务未启用，则不启动
            if (!task.enabled) {
                debug(`任务 ${id} 未启用，不启动`);
                return false;
            }

            debug(`启动任务: ${id}`);
            
            // 更新任务状态
            task.state = TaskState.RUNNING;
            task.lastRun = new Date();
            
            // 执行任务
            try {
                // 如果任务有间隔时间，则设置定时器
                if (task.interval > 0) {
                    const timer = setInterval(async () => {
                        try {
                            await task.run();
                        } catch (error) {
                            debug(`任务 ${id} 执行出错:`, error);
                            task.state = TaskState.FAILED;
                            
                            // 触发事件
                            if (EventBus) {
                                EventBus.emit('automation:taskFailed', { id, task, error });
                            }
                        }
                    }, task.interval);
                    
                    this.timers.set(id, timer);
                } else {
                    // 如果没有间隔时间，则只执行一次
                    await task.run();
                    task.state = TaskState.COMPLETED;
                }
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('automation:taskStarted', { id, task });
                }
                
                return true;
            } catch (error) {
                debug(`启动任务 ${id} 失败:`, error);
                task.state = TaskState.FAILED;
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('automation:taskFailed', { id, task, error });
                }
                
                return false;
            }
        }

        /**
         * 停止自动化任务
         * @param {string} id - 任务ID
         * @returns {boolean} 是否停止成功
         */
        stopTask(id) {
            if (!this.tasks.has(id)) {
                debug(`任务 ${id} 不存在`);
                return false;
            }

            if (!this.timers.has(id)) {
                debug(`任务 ${id} 未在运行`);
                return true;
            }

            debug(`停止任务: ${id}`);
            
            // 清除定时器
            clearInterval(this.timers.get(id));
            this.timers.delete(id);
            
            // 更新任务状态
            const task = this.tasks.get(id);
            task.state = TaskState.PAUSED;
            
            // 触发事件
            if (EventBus) {
                EventBus.emit('automation:taskStopped', { id, task });
            }
            
            return true;
        }

        /**
         * 加载自动化设置
         * @returns {Promise<Object>} 设置对象
         */
        async loadSettings() {
            try {
                if (!Storage) {
                    debug('存储模块未加载，使用默认设置');
                    this.settings = {
                        autoReplyEnabled: false,
                        autoTransferEnabled: false,
                        autoStart: true
                    };
                    return this.settings;
                }
                
                // 从存储中加载设置
                const settings = await Storage.get('automationSettings', {
                    autoReplyEnabled: false,
                    autoTransferEnabled: false,
                    autoStart: true
                });
                
                this.settings = settings;
                debug('加载自动化设置:', this.settings);
                
                // 更新任务启用状态
                if (this.tasks.has('autoReply')) {
                    const task = this.tasks.get('autoReply');
                    task.enabled = settings.autoReplyEnabled;
                }
                
                if (this.tasks.has('autoTransfer')) {
                    const task = this.tasks.get('autoTransfer');
                    task.enabled = settings.autoTransferEnabled;
                }
                
                return this.settings;
            } catch (error) {
                debug('加载自动化设置失败:', error);
                return {};
            }
        }

        /**
         * 保存自动化设置
         * @param {Object} settings - 设置对象
         * @returns {Promise<boolean>} 是否保存成功
         */
        async saveSettings(settings) {
            try {
                if (!Storage) {
                    debug('存储模块未加载，无法保存设置');
                    return false;
                }
                
                // 合并设置
                this.settings = { ...this.settings, ...settings };
                
                // 保存到存储
                await Storage.set('automationSettings', this.settings);
                debug('保存自动化设置:', this.settings);
                
                // 更新任务启用状态
                if (this.tasks.has('autoReply')) {
                    const task = this.tasks.get('autoReply');
                    task.enabled = this.settings.autoReplyEnabled;
                    
                    // 根据启用状态启动或停止任务
                    if (this.isRunning) {
                        if (task.enabled) {
                            await this.runTask('autoReply');
                        } else {
                            this.stopTask('autoReply');
                        }
                    }
                }
                
                if (this.tasks.has('autoTransfer')) {
                    const task = this.tasks.get('autoTransfer');
                    task.enabled = this.settings.autoTransferEnabled;
                }
                
                // 触发事件
                if (EventBus) {
                    EventBus.emit('automation:settingsUpdated', { settings: this.settings });
                }
                
                return true;
            } catch (error) {
                debug('保存自动化设置失败:', error);
                return false;
            }
        }

        /**
         * 切换自动回复状态
         * @param {boolean} enabled - 是否启用
         * @returns {Promise<boolean>} 是否成功
         */
        async toggleAutoReply(enabled = null) {
            const newEnabled = enabled !== null ? enabled : !this.settings.autoReplyEnabled;
            
            // 保存设置
            await this.saveSettings({ autoReplyEnabled: newEnabled });
            
            // 根据新状态启动或停止任务
            if (this.isRunning) {
                if (newEnabled) {
                    await this.runTask('autoReply');
                } else {
                    this.stopTask('autoReply');
                }
            }
            
            return newEnabled;
        }

        /**
         * 切换自动转人工状态
         * @param {boolean} enabled - 是否启用
         * @returns {Promise<boolean>} 是否成功
         */
        async toggleAutoTransfer(enabled = null) {
            const newEnabled = enabled !== null ? enabled : !this.settings.autoTransferEnabled;
            
            // 保存设置
            await this.saveSettings({ autoTransferEnabled: newEnabled });
            
            return newEnabled;
        }

        /**
         * 检查消息是否需要转人工
         * @param {Object} message - 消息对象
         * @param {Object} state - 会话状态
         * @returns {Promise<boolean>} 是否需要转人工
         */
        async checkShouldTransfer(message, state) {
            // 如果自动转人工未启用，则不转人工
            if (!this.settings.autoTransferEnabled) {
                return false;
            }
            
            // 委托给Transfer模块处理
            if (Transfer && Transfer.TransferManager) {
                return await Transfer.TransferManager.checkShouldTransfer(message, state);
            }
            
            return false;
        }

        /**
         * 运行自动回复任务
         * @private
         */
        async _runAutoReplyTask() {
            // 此处不需要实现具体逻辑，因为自动回复由消息处理模块触发
            return true;
        }

        /**
         * 运行自动转人工任务
         * @private
         */
        async _runAutoTransferTask() {
            // 此处不需要实现具体逻辑，因为自动转人工由消息处理模块触发
            return true;
        }

        /**
         * 运行检查未读消息任务
         * @private
         */
        async _runCheckUnreadTask() {
            // 如果平台模块已加载，则委托给平台模块处理
            const PlatformManager = global.AIPDD?.Platforms?.PlatformManager;
            if (PlatformManager) {
                const currentPlatform = PlatformManager.getCurrentPlatform();
                if (currentPlatform && typeof currentPlatform.checkUnreadMessages === 'function') {
                    await currentPlatform.checkUnreadMessages();
                    return true;
                }
            }
            
            // 兼容旧版本
            if (typeof window.checkUnreadMessages === 'function') {
                await window.checkUnreadMessages();
            }
            
            return true;
        }

        /**
         * 运行健康检查任务
         * @private
         */
        async _runHealthCheckTask() {
            // 如果有健康检查函数，则调用
            if (typeof window.performHealthCheck === 'function') {
                await window.performHealthCheck();
            }
            
            return true;
        }
    }

    // 创建单例实例
    const automationManager = new AutomationManager();

    // 导出模块
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Core) global.AIPDD.Core = {};
    if (!global.AIPDD.Core.Automation) global.AIPDD.Core.Automation = {};
    
    global.AIPDD.Core.Automation.AutomationManager = automationManager;
    global.AIPDD.Core.Automation.TaskState = TaskState;

})(window); 