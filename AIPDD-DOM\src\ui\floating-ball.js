 /**
 * 浮动球UI组件
 * 负责在页面上显示浮动控制球，提供自动回复开关和设置入口
 */
(function() {
    'use strict';

    // 确保命名空间存在
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.UI = window.AIPDD.UI || {};

    // 依赖检查
    const Dependencies = {
        Debug: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.Debug,
        EventBus: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.EventBus,
        Storage: window.AIPDD.Core && window.AIPDD.Core.Storage && window.AIPDD.Core.Storage.StorageManager
    };

    // 记录日志的辅助函数
    function log(message, level = 'info') {
        if (Dependencies.Debug) {
            Dependencies.Debug.log(message, 'floating-ball', level);
        } else {
            console.log(`[浮球] ${message}`);
        }
    }

    /**
     * 浮动球类
     * 管理页面上的浮动控制球
     */
    class FloatingBall {
        /**
         * 构造函数
         */
        constructor() {
            this.ball = null;
            this.isDragging = false;
            this.startX = 0;
            this.startY = 0;
            this.ballRect = null;
            this.currentStatus = null;
            this.menuVisible = false;
            this.autoReplyEnabled = false;  // 默认关闭自动回复
            
            // 绑定方法
            this.onMouseDown = this.onMouseDown.bind(this);
            this.onMouseMove = this.onMouseMove.bind(this);
            this.onMouseUp = this.onMouseUp.bind(this);
            
            log('浮动球组件已初始化');
        }

        /**
         * 创建浮动球
         */
        create() {
            try {
                // 检查是否已存在浮球
                if (document.querySelector('.floating-ball')) {
                    log('浮球已存在');
                    return;
                }

                // 加载样式
                this._loadStyles();

                // 创建浮球容器
                this.ball = document.createElement('div');
                this.ball.className = 'floating-ball';
                this.ball.innerHTML = `
                    <div class="icon-wrapper">
                        <img src="${chrome.runtime.getURL('icons/icon48.png')}" alt="AI助手" />
                    </div>
                    <div class="floating-menu">
                        <div class="menu-item" data-action="toggle-auto-reply">
                            <span class="menu-icon">🤖</span>
                            <span class="status-text">自动回复已关闭</span>
                            <span class="toggle-switch"></span>
                        </div>
                        <div class="menu-divider"></div>
                        <div class="menu-item" data-action="ai-settings">
                            <span class="menu-icon">⚛️</span>
                            <span>AI设置</span>
                        </div>
                        <div class="menu-item" data-action="settings">
                            <span class="menu-icon">🔁</span>
                            <span>转人工设置</span>
                        </div>
                        <div class="menu-item" data-action="other-settings">
                            <span class="menu-icon">🎛️</span>
                            <span>其他设置</span>
                        </div>
                        <div class="menu-item" data-action="about">
                            <span class="menu-icon">ℹ️</span>
                            <span>关于</span>
                        </div>
                    </div>
                `;

                // 添加到页面
                document.body.appendChild(this.ball);

                // 设置事件监听
                this.setupEventListeners();

                // 初始化状态
                this.initState();

                log('浮球创建成功');
            } catch (error) {
                log(`创建失败: ${error.message}`, 'error');
            }
        }

        /**
         * 加载浮动球样式
         * @private
         */
        _loadStyles() {
            try {
                // 检查样式是否已加载
                if (document.querySelector('#aipdd-floating-ball-styles')) {
                    return;
                }
                
                // 创建样式元素
                const style = document.createElement('link');
                style.id = 'aipdd-floating-ball-styles';
                style.rel = 'stylesheet';
                style.type = 'text/css';
                style.href = chrome.runtime.getURL('src/styles/floating-ball.css');
                
                // 添加到页面
                document.head.appendChild(style);
                
                log('浮球样式已加载');
            } catch (error) {
                log(`加载样式失败: ${error.message}`, 'error');
            }
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 拖拽事件
            this.ball.addEventListener('mousedown', this.onMouseDown);
            
            // 菜单显示/隐藏
            this.ball.addEventListener('mouseenter', () => this.toggleMenu(true));
            this.ball.addEventListener('mouseleave', () => this.toggleMenu(false));
            
            // 菜单项点击
            const menu = this.ball.querySelector('.floating-menu');
            menu.addEventListener('click', (e) => {
                const menuItem = e.target.closest('.menu-item');
                if (menuItem) {
                    e.stopPropagation();
                    this.handleMenuClick(e);
                }
            });
        }

        /**
         * 鼠标按下事件处理
         * @param {MouseEvent} e - 鼠标事件
         */
        onMouseDown(e) {
            if (e.target.closest('.menu-item') || e.target.closest('.floating-menu')) return;
            
            this.isDragging = true;
            this.ballRect = this.ball.getBoundingClientRect();
            this.startX = e.clientX - this.ballRect.left;
            this.startY = e.clientY - this.ballRect.top;
            
            this.ball.style.transition = 'none';
            document.addEventListener('mousemove', this.onMouseMove);
            document.addEventListener('mouseup', this.onMouseUp);
        }

        /**
         * 鼠标移动事件处理
         * @param {MouseEvent} e - 鼠标事件
         */
        onMouseMove(e) {
            if (!this.isDragging) return;
            
            const x = e.clientX - this.startX;
            const y = e.clientY - this.startY;
            
            const maxX = window.innerWidth - this.ball.offsetWidth;
            const maxY = window.innerHeight - this.ball.offsetHeight;
            
            this.ball.style.left = Math.min(Math.max(0, x), maxX) + 'px';
            this.ball.style.top = Math.min(Math.max(0, y), maxY) + 'px';
            this.ball.style.right = 'auto';
            this.ball.style.transform = 'none';
        }

        /**
         * 鼠标释放事件处理
         */
        onMouseUp() {
            this.isDragging = false;
            this.ball.style.transition = '';
            document.removeEventListener('mousemove', this.onMouseMove);
            document.removeEventListener('mouseup', this.onMouseUp);
        }

        /**
         * 切换菜单显示状态
         * @param {boolean|null} show - 是否显示菜单，null表示切换状态
         */
        toggleMenu(show = null) {
            const menu = this.ball.querySelector('.floating-menu');
            this.menuVisible = show !== null ? show : !this.menuVisible;
            
            if (this.menuVisible) {
                menu.style.display = 'block';
                menu.offsetHeight; // 强制重绘
                menu.classList.add('visible');
            } else {
                menu.classList.remove('visible');
                setTimeout(() => {
                    if (!this.menuVisible) {
                        menu.style.display = 'none';
                    }
                }, 300);
            }
        }

        /**
         * 处理菜单点击事件
         * @param {MouseEvent} e - 鼠标事件
         */
        handleMenuClick(e) {
            const menuItem = e.target.closest('.menu-item');
            if (!menuItem) return;
            
            const action = menuItem.dataset.action;
            switch (action) {
                case 'toggle-auto-reply':
                    this.toggleAutoReply();
                    break;
                    
                case 'ai-settings':
                    this.showAISettings();
                    break;
                    
                case 'settings':
                    this._triggerEvent('settings:show');
                    break;
                    
                case 'other-settings':
                    this._triggerEvent('other-settings:show');
                    break;
                    
                case 'about':
                    this._triggerEvent('about:show');
                    break;
            }
        }

        /**
         * 触发事件
         * @param {string} eventName - 事件名称
         * @param {Object} data - 事件数据
         * @private
         */
        _triggerEvent(eventName, data = {}) {
            if (Dependencies.EventBus) {
                Dependencies.EventBus.emit(eventName, data);
            } else {
                log(`事件总线不可用，无法触发事件: ${eventName}`, 'warn');
                // 作为临时解决方案，可以使用自定义事件
                const event = new CustomEvent(`AIPDD:${eventName}`, { detail: data });
                document.dispatchEvent(event);
            }
        }
        
        /**
         * 初始化状态
         */
        async initState() {
            try {
                //初始化自动回复状态,从storage中获取
                let enabled = false;
                
                if (Dependencies.Storage) {
                    enabled = await Dependencies.Storage.get('autoReplyEnabled', false);
                } else {
                    // 兼容旧版存储
                    enabled = await new Promise(resolve => {
                        chrome.storage.local.get(['autoReplyEnabled'], function(result) {
                            resolve(result.autoReplyEnabled || false);
                        });
                    });
                }
                
                this.autoReplyEnabled = enabled;
                this.updateUIStatus(enabled);
                log(`初始化状态完成，自动回复${enabled ? '已开启' : '已关闭'}`);
            } catch (error) {
                log(`初始化状态失败: ${error.message}`, 'error');
            }
        }

        /**
         * 更新UI状态
         * @param {boolean} enabled - 是否启用自动回复
         */
        updateUIStatus(enabled) {
            if (this.ball) {
                const statusText = this.ball.querySelector('.status-text');
                const toggle = this.ball.querySelector('.toggle-switch');
                
                if (enabled) {
                    this.ball.classList.add('auto-reply-active');
                    if (statusText) statusText.textContent = '自动回复已开启';
                    if (toggle) toggle.classList.add('active');
                } else {
                    this.ball.classList.remove('auto-reply-active');
                    if (statusText) statusText.textContent = '自动回复已关闭';
                    if (toggle) toggle.classList.remove('active');
                }
            }
        }
        
        /**
         * 切换自动回复状态
         */
        async toggleAutoReply() {
            try {
                const currentState = this.autoReplyEnabled;
                const newState = !currentState;
                
                // 保存状态
                if (Dependencies.Storage) {
                    await Dependencies.Storage.set('autoReplyEnabled', newState);
                } else {
                    // 兼容旧版存储
                    await new Promise(resolve => {
                        chrome.storage.local.set({ 'autoReplyEnabled': newState }, resolve);
                    });
                }
                
                this.autoReplyEnabled = newState;
                this.updateUIStatus(newState);
                
                // 触发状态变更事件
                this._triggerEvent('auto-reply:changed', { 
                    enabled: newState 
                });
                
                log(`自动回复已${newState ? '开启' : '关闭'}`);
            } catch (error) {
                log(`切换自动回复状态失败: ${error.message}`, 'error');
            }
        }

        /**
         * 设置状态显示
         * @param {string} status - 状态类型 (normal, processing, success, warning, error)
         * @param {number} duration - 显示持续时间(毫秒)
         */
        setStatus(status, duration = 1500) {
            if (this.currentStatus === status) return;
            
            // 移除当前状态
            if (this.currentStatus) {
                this.ball.classList.remove(`status-${this.currentStatus}`);
            }
            
            // 设置新状态
            if (status) {
                this.ball.classList.add(`status-${status}`);
                this.currentStatus = status;
                
                // 如果提供了持续时间，自动恢复到普通状态
                if (duration > 0) {
                    setTimeout(() => {
                        this.setStatus('normal');
                    }, duration);
                }
            } else {
                this.currentStatus = null;
            }
        }

        /**
         * 显示AI设置窗口
         */
        async showAISettings() {
            this._triggerEvent('ai-settings:show');
        }
    }

    // 导出到命名空间
    window.AIPDD.UI.FloatingBall = new FloatingBall();

    // 调试日志
    log('浮动球模块已加载');
})();