// dom-utils.js - DOM操作工具模块
(function() {
    // 命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Utils = window.AIPDD.Utils || {};
    
    // DOM操作工具
    const DOMUtils = {
        /**
         * 查找单个元素
         * @param {string} selector - 选择器
         * @param {Element} parent - 父元素
         * @returns {Element|null} 找到的元素
         */
        findElement: function(selector, parent = document) {
            return parent.querySelector(selector);
        },
        
        /**
         * 查找多个元素
         * @param {string} selector - 选择器
         * @param {Element} parent - 父元素
         * @returns {Array<Element>} 元素数组
         */
        findElements: function(selector, parent = document) {
            return Array.from(parent.querySelectorAll(selector));
        },
        
        /**
         * 通过XPath查找元素
         * @param {string} xpath - XPath表达式
         * @param {Element} parent - 父元素
         * @returns {Element|null} 找到的元素
         */
        findByXPath: function(xpath, parent = document) {
            const result = document.evaluate(
                xpath,
                parent,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            );
            return result.singleNodeValue;
        },
        
        /**
         * 通过XPath查找多个元素
         * @param {string} xpath - XPath表达式
         * @param {Element} parent - 父元素
         * @returns {Array<Element>} 元素数组
         */
        findAllByXPath: function(xpath, parent = document) {
            const result = document.evaluate(
                xpath,
                parent,
                null,
                XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                null
            );
            
            const elements = [];
            for (let i = 0; i < result.snapshotLength; i++) {
                elements.push(result.snapshotItem(i));
            }
            return elements;
        },
        
        /**
         * 创建元素
         * @param {string} tagName - 标签名
         * @param {Object} attributes - 属性对象
         * @param {string} textContent - 文本内容
         * @returns {Element} 创建的元素
         */
        createElement: function(tagName, attributes = {}, textContent = '') {
            const element = document.createElement(tagName);
            
            // 设置属性
            for (const [key, value] of Object.entries(attributes)) {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'innerHTML') {
                    element.innerHTML = value;
                } else {
                    element.setAttribute(key, value);
                }
            }
            
            // 设置文本内容
            if (textContent) {
                element.textContent = textContent;
            }
            
            return element;
        },
        
        /**
         * 移除元素
         * @param {Element|string} elementOrSelector - 元素或选择器
         */
        removeElement: function(elementOrSelector) {
            let element;
            if (typeof elementOrSelector === 'string') {
                element = document.querySelector(elementOrSelector);
            } else {
                element = elementOrSelector;
            }
            
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        },
        
        /**
         * 检查元素是否存在
         * @param {string} selector - 选择器
         * @param {Element} parent - 父元素
         * @returns {boolean} 是否存在
         */
        exists: function(selector, parent = document) {
            return parent.querySelector(selector) !== null;
        },
        
        /**
         * 滚动到元素
         * @param {Element} element - 目标元素
         * @param {Object} options - 滚动选项
         */
        scrollToElement: function(element, options = {}) {
            if (!element) return;
            
            const defaultOptions = {
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            };
            
            element.scrollIntoView({ ...defaultOptions, ...options });
        },
        
        /**
         * 触发事件
         * @param {Element} element - 目标元素
         * @param {string} eventType - 事件类型
         * @param {Object} options - 事件选项
         */
        triggerEvent: function(element, eventType, options = {}) {
            if (!element) return;
            
            const defaultOptions = {
                bubbles: true,
                cancelable: true,
                view: window
            };
            
            const event = new Event(eventType, { ...defaultOptions, ...options });
            element.dispatchEvent(event);
        },
        
        /**
         * 触发鼠标事件
         * @param {Element} element - 目标元素
         * @param {string} eventType - 事件类型
         * @param {Object} options - 事件选项
         */
        triggerMouseEvent: function(element, eventType, options = {}) {
            if (!element) return;
            
            const defaultOptions = {
                bubbles: true,
                cancelable: true,
                view: window
            };
            
            const event = new MouseEvent(eventType, { ...defaultOptions, ...options });
            element.dispatchEvent(event);
        },
        
        /**
         * 获取元素的文本内容（去除空白）
         * @param {Element} element - 目标元素
         * @returns {string} 文本内容
         */
        getTextContent: function(element) {
            if (!element) return '';
            return (element.textContent && element.textContent.trim()) || '';
        },
        
        /**
         * 检查元素是否包含指定类名
         * @param {Element} element - 目标元素
         * @param {string} className - 类名
         * @returns {boolean} 是否包含
         */
        hasClass: function(element, className) {
            if (!element) return false;
            return element.classList.contains(className);
        },
        
        /**
         * 添加类名
         * @param {Element} element - 目标元素
         * @param {string|Array<string>} classNames - 类名或类名数组
         */
        addClass: function(element, classNames) {
            if (!element) return;
            
            if (Array.isArray(classNames)) {
                element.classList.add(...classNames);
            } else {
                element.classList.add(classNames);
            }
        },
        
        /**
         * 移除类名
         * @param {Element} element - 目标元素
         * @param {string|Array<string>} classNames - 类名或类名数组
         */
        removeClass: function(element, classNames) {
            if (!element) return;
            
            if (Array.isArray(classNames)) {
                element.classList.remove(...classNames);
            } else {
                element.classList.remove(classNames);
            }
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Utils.DOM = DOMUtils;
    
    // 为了向下兼容，暴露常用函数到全局
    window.findElement = DOMUtils.findElement;
    window.findElements = DOMUtils.findElements;
})();
