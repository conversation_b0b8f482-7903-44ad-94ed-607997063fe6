// 显示转人工设置浮窗
function showSettingsWindow() {
    const settingsContent = `
        <div class="settings-container">
            <div class="section">
                <div class="section-title">转人工设置</div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            自动转人工总开关
                            <div class="option-description">根据设定的规则自动转接人工客服，保存后生效</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="manualEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item">
                        <div class="option-label">
                            关键词触发转人工
                            <div class="option-description">当客户输入内容包含以下关键词时自动转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="keywordEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item keyword-management" style="border-bottom: none;margin-top: -6px;">
                        <div class="keyword-input-group">
                            <input type="text" id="newKeyword" placeholder="输入触发转人工关键词">
                            <button id="addKeyword" class="keyword-btn">添加</button>
                        </div>
                        <div id="keywordList" class="keyword-list"></div>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            多媒体消息转人工
                            <div class="option-description">当客户发送图片/视频触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="imageEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            退款场景转人工
                            <div class="option-description">涉及退款相关问题时触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="refundEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            投诉场景转人工
                            <div class="option-description">客户投诉或表达不满时触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="complaintEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div  class="option-item keyword-management" >
                        <div style="display:flex;">
                        <div class="option-label">
                            AI智能判断转人工
                            <div class="option-description">可在知识库添加触发条件，由AI进行判断自动转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="difyTransferEnabled" checked>
                            <span class="slider"></span>
                        </label>
                       </div>
                       <div id="diykeywordList" class="keyword-list">
                            <div class="keyword-item">
                                <span>转人工</span>
                            </div>
                            <div class="keyword-item">
                                <span>转售前</span>
                            </div>
                            <div class="keyword-item">
                                <span>转售后</span>
                            </div>
                            <div class="keyword-item">
                                <span>转接</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="option-label">
                            自动转人工已关闭后处理方式
                             <div class="option-description">可设置发送提示语和自动标记星标</div>
                        </div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="transfer-fail-option">
                            <div class="transfer-fail-option-header">
                                <input type="checkbox" id="transferFailOption1" name="transferFailOptions" value="message" class="transfer-fail-checkbox" checked>
                                <label for="transferFailOption1">发送提示语</label>
                                <input type="checkbox" id="transferFailOption2" name="transferFailOptions" value="star" class="transfer-fail-checkbox">
                                <label for="transferFailOption2">自动标记星标</label>
                            </div>
                            <div class="keyword-input-group">
                                <input type="text" id="transferFailMessage" placeholder="请输入提示语" value="实在抱歉无法转接，请稍候再联系" class="transfer-fail-input">
                                <button id="transferFailMessageSave" class="transfer-fail-btn">修改</button>
                            </div>
                    </div>
                        <div id="transferFailMessageSaveSuccess" class="save-success">
                            <span class="settings-saved-tip">
                                 设置已保存
                            </span>
                        </div>
                    </div>
                    <div class="option-item" style="margin-top:-10px;">
                        <div class="option-label">
                            转人工客服失败后处理方式
                             <div class="option-description">转接异常或人工客服不在线时，发送提示语和打标星标</div>
                        </div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="transfer-fail-option">
                            <div class="transfer-fail-option-header">
                                <input type="checkbox" id="transferErrorOption1" name="transferErrorOptions" value="message" class="transfer-fail-checkbox" checked>
                                <label for="transferErrorOption1">发送提示语</label>
                                <input type="checkbox" id="transferErrorOption2" name="transferErrorOptions" value="star" class="transfer-fail-checkbox" checked>
                                <label for="transferErrorOption2">自动标记星标</label>
                            </div>
                            <div class="keyword-input-group">
                                <input type="text" id="transferErrorMessage" placeholder="请输入提示语" value="抱歉客服暂时离开，稍后为您处理" class="transfer-fail-input">
                                <button id="transferErrorMessageSave" class="transfer-fail-btn">修改</button>
                            </div>
                        </div>
                        <div id="transferErrorMessageSaveSuccess" class="save-success">
                            <span class="settings-saved-tip">
                                 设置已保存
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">人工客服设置</div>
                <div class="option-group">
                    <div class="option-item keyword-management">
                        <div class="main-switch-label">
                            转接到指定账号
                             <div class="option-description">请填客服账号或账号备注，多个账号随机选一个进行转接</div>
                        </div>
                        <div class="keyword-input-group">
                            <input type="text" id="targetServiceAccount" placeholder="输入当前店铺的一个客服账号">
                            <button id="addServiceAccount" class="keyword-btn">添加</button>
                        </div>
                        <div id="serviceAccountList" class="keyword-list"></div>
                        <div class="error-message" style="display: none;flex:100%;color:red;" id="targetServiceAccountError">请至少添加一个转接客服账号</div>
                    </div>
                    <!-- 暂时注释掉转接到分组功能
                    <div class="option-item" style="display: flex; align-items: center;">
                        <div class="main-switch-label">
                            转接到分组
                            <div class="option-description">选择一个客服分组，同时平台上创建对应名称客服分组</div>
                        </div>
                        <select id="defaultGroup" style="height: 36px;">
                            <option value="presales">售前组</option>
                            <option value="aftersales">售后组</option>
                        </select>
                    </div>
                    -->
                </div>
            </div>

            <button class="save-btn" id="saveSettings">保存设置</button>
            <div class="save-success" id="settingsSaveSuccess" style="display: none; text-align: center; margin-top: 12px;">
                <span class="settings-saved-tip">
                     设置已保存
                </span>
            </div>
        </div>
    `;

    const modal = createFloatingWindow('AI客服助手设置', settingsContent);

    // 加载设置
    loadSettings();
    
    // 加载关键词列表
    loadKeywordList();
    
    // 加载服务账号列表
    loadServiceAccountList();
    
    // 添加转人工相关设置保存功能
    const transferErrorMessageSave = document.getElementById('transferErrorMessageSave');
    const transferFailMessageSave = document.getElementById('transferFailMessageSave');
    const transferFailMessageSaveSuccess = document.getElementById('transferFailMessageSaveSuccess');
    const transferErrorMessageSaveSuccess = document.getElementById('transferErrorMessageSaveSuccess');
    
    // 转人工失败提示语保存按钮
    if (transferErrorMessageSave) {
        transferErrorMessageSave.addEventListener('click', async () => {
            const message = document.getElementById('transferErrorMessage').value;
            if (!message) return;
            
            const settings = await StateManager.getState('transferSettings', {});
            await StateManager.setState('transferSettings', {
                ...settings,
                transferErrorMessage: message
            });
            
            if (transferErrorMessageSaveSuccess) {
                // 显示保存成功提示，使用opacity实现淡入淡出效果
                transferErrorMessageSaveSuccess.style.display = 'block';
                transferErrorMessageSaveSuccess.style.opacity = '1';
                
                // 2秒后淡出
                setTimeout(() => {
                    transferErrorMessageSaveSuccess.style.opacity = '0';
                    setTimeout(() => {
                        transferErrorMessageSaveSuccess.style.display = 'none';
                    }, 300);
                }, 2000);
            }
        });
    }
    
    // 关闭自动转人工提示语保存按钮
    if (transferFailMessageSave) {
        transferFailMessageSave.addEventListener('click', async () => {
            const message = document.getElementById('transferFailMessage').value;
            if (!message) return;
            
            const settings = await StateManager.getState('transferSettings', {});
            await StateManager.setState('transferSettings', {
                ...settings,
                transferFailMessage: message
            });
            
            if (transferFailMessageSaveSuccess) {
                // 显示保存成功提示，使用opacity实现淡入淡出效果
                transferFailMessageSaveSuccess.style.display = 'block';
                transferFailMessageSaveSuccess.style.opacity = '1';
                
                // 2秒后淡出
                setTimeout(() => {
                    transferFailMessageSaveSuccess.style.opacity = '0';
                    setTimeout(() => {
                        transferFailMessageSaveSuccess.style.display = 'none';
                    }, 300);
                }, 2000);
            }
        });
    }
    
    // 监听复选框变化，自动保存选项
    const checkboxOptions = [
        'transferErrorOption1', 'transferErrorOption2',
        'transferFailOption1', 'transferFailOption2'
    ];
    
    checkboxOptions.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.addEventListener('change', async () => {
                const settings = await StateManager.getState('transferSettings', {});
                
                // 根据复选框 ID 确定更新哪个选项
                if (id.startsWith('transferError')) {
                    // 更新转人工失败处理选项
                    const options = [
                        document.getElementById('transferErrorOption1')?.checked ? 'message' : '',
                        document.getElementById('transferErrorOption2')?.checked ? 'star' : ''
                    ].filter(Boolean);
                    
                    await StateManager.setState('transferSettings', {
                        ...settings,
                        transferErrorOptions: options
                    });
                } else if (id.startsWith('transferFail')) {
                    // 更新关闭自动转人工处理选项
                    const options = [
                        document.getElementById('transferFailOption1')?.checked ? 'message' : '',
                        document.getElementById('transferFailOption2')?.checked ? 'star' : ''
                    ].filter(Boolean);
                    
                    await StateManager.setState('transferSettings', {
                        ...settings,
                        transferFailOptions: options
                    });
                }
            });
        }
    });
    
    //增加添加关键词按钮的事件处理————modify by rice
    //2025-01-08
    const addKeywordButton = modal.querySelector('#addKeyword');
    addKeywordButton.onclick = addTransferKeyword;
    
    // 添加服务账号按钮事件
    const addServiceAccountButton = modal.querySelector('#addServiceAccount');
    if (addServiceAccountButton) {
        addServiceAccountButton.onclick = addServiceAccount;
    }

    // 添加保存按钮事件
    const saveButton = modal.querySelector('#saveSettings');
    saveButton.onclick = saveSettings;
    
    // 添加转人工失败提示文案保存按钮事件
    const transferFailMessageSaveButton = modal.querySelector('#transferFailMessageSave');
    if (transferFailMessageSaveButton) {
        transferFailMessageSaveButton.onclick = saveOtherSettings;
    }
    
    // 加载转人工失败提示文案
    chrome.storage.local.get('transferFailMessage').then(res => {
        if (res.transferFailMessage) {
            const transferFailMessageInput = document.querySelector('#transferFailMessage');
            if (transferFailMessageInput) {
                transferFailMessageInput.value = res.transferFailMessage;
            }
        } else {
            // 设置默认值
            const transferFailMessageInput = document.querySelector('#transferFailMessage');
            if (transferFailMessageInput) {
                transferFailMessageInput.value = '实在抱歉无法转接，请稍候再联系';
                // 保存默认值
                chrome.storage.local.set({ transferFailMessage: '实在抱歉无法转接，请稍候再联系' });
            }
        }
    }).catch(error => {
        // 出错时设置默认值
        const transferFailMessageInput = document.querySelector('#transferFailMessage');
        if (transferFailMessageInput) {
            transferFailMessageInput.value = '实在抱歉无法转接，请稍候再联系';
        }
    });
}

// 转人工状态枚举
const TransferState = {
    NONE: 0,          // 未转人工
    TRANSFERRING: 1,  // 正在转人工
    TRANSFERRED: 2,   // 已转人工
    FAILED: 3         // 转人工失败
};

// 消息类型定义
const MESSAGE_TYPES = {
    TEXT: 'text',
    IMAGE: 'image',
    LINK: 'link',
    EMOJI: 'emoji',
    PRODUCT: 'product', 
    REFUND: 'refund',
    UNKNOWN: 'unknown',
    ORDER: 'order',
    LEGOCARD: 'legocard',
    PRODUCT_COMMENT: 'product_comment',
    GOODS_COMMENT: 'goods_comment'
};

// 调试输出函数
function debug(...args) {
    if (window.debugMode) {
        console.log(`[DEBUG][${getCurrentTime()}]`, ...args);
    }
}

// 获取当前时间函数
function getCurrentTime() {
    const now = new Date();
    return now.toLocaleTimeString('zh-CN', { hour12: false }) + '.' + now.getMilliseconds().toString().padStart(3, '0');
}

// 睡眠函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 在新聊天窗口中创建浮动窗口
function createFloatingWindow(title, content) {
    // 检查是否已存在相同标题的窗口
    const existingWindow = document.querySelector(`.floating-window[data-title="${title}"]`);
    if (existingWindow) {
        // 如果存在，聚焦并返回
        existingWindow.style.display = 'block';
        existingWindow.style.opacity = '1';
        return existingWindow;
    }
    
    // 创建浮动窗口容器
    const floatingWindow = document.createElement('div');
    floatingWindow.className = 'floating-window';
    floatingWindow.setAttribute('data-title', title);
    
    // 添加窗口头部
    const windowHeader = document.createElement('div');
    windowHeader.className = 'window-header';
    
    // 添加标题
    const windowTitle = document.createElement('div');
    windowTitle.className = 'window-title';
    windowTitle.textContent = title;
    
    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.className = 'window-close';
    closeButton.innerHTML = '×';
    closeButton.onclick = function() {
        floatingWindow.style.opacity = '0';
        setTimeout(() => {
            floatingWindow.style.display = 'none';
        }, 300);
    };
    
    // 组装头部
    windowHeader.appendChild(windowTitle);
    windowHeader.appendChild(closeButton);
    
    // 添加内容区域
    const windowContent = document.createElement('div');
    windowContent.className = 'window-content';
    windowContent.innerHTML = content;
    
    // 组装窗口
    floatingWindow.appendChild(windowHeader);
    floatingWindow.appendChild(windowContent);
    
    // 添加到文档
    document.body.appendChild(floatingWindow);
    
    // 实现拖动功能
    let isDragging = false;
    let offsetX, offsetY;
    
    windowHeader.onmousedown = function(e) {
        isDragging = true;
        offsetX = e.clientX - floatingWindow.offsetLeft;
        offsetY = e.clientY - floatingWindow.offsetTop;
    };
    
    document.onmousemove = function(e) {
        if (!isDragging) return;
        
        floatingWindow.style.left = (e.clientX - offsetX) + 'px';
        floatingWindow.style.top = (e.clientY - offsetY) + 'px';
    };
    
    document.onmouseup = function() {
        isDragging = false;
    };
    
    return floatingWindow;
}

/**
 * 处理转人工请求
 * @param {string} conversationId 会话ID
 * @returns {Promise<boolean>} 转人工是否成功
 */
async function handleTransfer(conversationId) {
    debug('[转人工] 开始处理转人工请求:', conversationId);
    
    // 最大重试次数
    const MAX_RETRIES = 3;
    let retryCount = 0;
    
    // 获取设置中指定的客服账号列表
    const settings = await StateManager.getState('transferSettings', {});
    let serviceAccounts = settings.serviceAccounts || [];
    
    // 兼容旧版本，如果没有serviceAccounts但有specifiedAgent，则使用specifiedAgent
    if (serviceAccounts.length === 0 && settings.specifiedAgent) {
        serviceAccounts = [settings.specifiedAgent.trim()];
    }
    
    // 复制一份账号列表，用于随机选择并移除已尝试过的账号
    let availableAccounts = [...serviceAccounts];
    
    while (retryCount < MAX_RETRIES) {
        try {
            // 查找转移按钮
            const transferButton = await findTransferButton();
            if (!transferButton) {
                throw new Error('未找到转移按钮');
            }
            
            debug('[转人工] 找到转移按钮，准备点击');
            // 确保按钮在视图中
            transferButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            
            // 模拟真实点击
            const rect = transferButton.getBoundingClientRect();
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            });
            transferButton.dispatchEvent(clickEvent);
            await sleep(2000);
            
            // 选择目标客服
            // 修改：不再在selectTargetAgent中随机选择客服，而是在这里循环尝试所有可用的客服账号
            let targetAgent = null;
            let accountTrials = 0;
            
            // 尝试可用的客服账号，直到找到一个或者全部尝试失败
            while (!targetAgent && availableAccounts.length > 0 && accountTrials < availableAccounts.length) {
                // 随机选择一个客服账号
                const randomIndex = Math.floor(Math.random() * availableAccounts.length);
                const selectedAccount = availableAccounts[randomIndex];
                
                // 从可用账号列表中移除，避免重复尝试
                availableAccounts.splice(randomIndex, 1);
                
                debug('[转人工] 尝试查找客服账号:', selectedAccount, '(第', accountTrials + 1, '次尝试)');
                
                // 尝试查找这个客服账号
                targetAgent = await selectTargetAgent(selectedAccount);
                accountTrials++;
                
                if (!targetAgent) {
                    debug('[转人工] 未找到客服账号:', selectedAccount);
                }
            }
            
            if (!targetAgent) {
                // 本次重试所有账号都尝试完毕但未找到客服
                throw new Error('未找到目标客服');
            }
            
            debug('[转人工] 找到目标客服，准备点击');
            targetAgent.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            targetAgent.click();
            await sleep(2000);
            
            // 查找并点击转移原因按钮
            const confirmButton = await findTransferReasonButton();
            if (!confirmButton) {
                throw new Error('未找到转移原因按钮');
            }
            
            debug('[转人工] 找到转移原因按钮，准备点击');
            confirmButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            confirmButton.click();
            await sleep(2000);
            
            // 检查是否出现"取消收藏并转移"按钮或意见反馈对话框
            const cancelStarButton = await findCancelStarButton();
            if (cancelStarButton) {
                debug('[转人工] 找到后续操作按钮，准备点击:', cancelStarButton.textContent);
                cancelStarButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await sleep(500);
                cancelStarButton.click();
                await sleep(2000);
            }
            
            // 更新状态为已转人工
            const state = conversationStates && conversationStates.get ? conversationStates.get(conversationId) : null;
            if (state) {
                state.transferState = TransferState.TRANSFERRED;
            }

            // 发送转人工成功提示
            await sendMessage('收到，请您稍候一下···');
            
            debug('[转人工] 转人工成功');
            
            // 不再刷新页面，直接返回成功
            return true;
            
        } catch (error) {
            debug('[转人工] 第', retryCount + 1, '次尝试失败:', error);
            retryCount++;
            
            // 如果还有账号可以尝试，继续尝试
            if (availableAccounts.length > 0) {
                debug('[转人工] 仍有', availableAccounts.length, '个客服账号可尝试');
            } else {
                // 重新填充账号列表以便下一次外层重试可以再次尝试所有账号
                availableAccounts = [...serviceAccounts];
                debug('[转人工] 所有客服账号都已尝试，将在下一次重试中重新尝试所有账号');
            }
            
            if (retryCount >= MAX_RETRIES) {
                debug('[转人工] 达到最大重试次数');
                // 更新状态为失败
                const state = conversationStates && conversationStates.get ? conversationStates.get(conversationId) : null;
                if (state) {
                    state.transferState = TransferState.FAILED;
                }
                
                // 自动关闭"自动转人工总开关"
                debug('[转人工] 所有客服账号尝试失败，自动关闭"自动转人工总开关"');
                const currentSettings = await StateManager.getState('transferSettings', {});
                
                // 更新设置，关闭自动转人工总开关
                await StateManager.setState('transferSettings', {
                    ...currentSettings,
                    manualEnabled: false
                });
                
                return await handleTransferFailed();
            }
            
            // 等待一段时间后重试
            await sleep(2000 * retryCount);
        }
    }
    
    return false;
}

/**
 * 检查是否应该转人工
 * @param {Object} message 消息对象
 * @param {Object} state 会话状态
 * @returns {Promise<boolean>} 是否应该转人工
 */
async function checkShouldTransfer(message, state) {
    // 获取设置
    const settings = await StateManager.getState('transferSettings', {
        manualEnabled: true,  // 总开关
        keywordEnabled: true, // 关键词触发
        imageEnabled: true,   // 图片触发
        refundEnabled: true,  // 退款触发
        complaintEnabled: true // 投诉触发
    });
    
    // 检查是否包含转人工关键词
    const isTransferKeyword = message.content && 
        (message.content.includes('转人工') || 
         message.content.includes('转接') || 
         message.content.includes('转售前') || 
         message.content.includes('转售后'));
    
    // 首先检查总开关状态
    if (!settings.manualEnabled) {
        debug('[转人工] 自动转人工总开关已关闭');
        // 只有当消息包含转人工关键词时才处理
        if (isTransferKeyword) {
            debug('[转人工] 检测到转人工关键词，处理已关闭自动转人工的情况');
            return await handleTransferClosed();
        }
        return false;
    }

    // 只有在总开关开启的情况下，才检查各个子开关
    if (settings.manualEnabled) {
        // 关键词触发转人工
        if (settings.keywordEnabled && message.content) {
            const keywords = await loadTransferKeywords();
            if (keywords.some(keyword => message.content.includes(keyword))) {
                debug('[转人工] 检测到转人工关键词，触发转人工');
                return true;
            }
        }

        // 图片消息转人工
        if (settings.imageEnabled && message.type === MESSAGE_TYPES.IMAGE) {
            debug('[转人工] 检测到图片消息，触发转人工');
            return true;
        }

        // 退款场景转人工
        if (settings.refundEnabled && 
            (message.type === MESSAGE_TYPES.REFUND )) {
            debug('[转人工] 检测到退款相关内容，触发转人工');
            return true;
        }

        // 投诉场景转人工
        if (settings.complaintEnabled && message.content) {
            const complaintKeywords = ['投诉', '差评', '不满意', '质量差', '态度差'];
            if (complaintKeywords.some(keyword => message.content.includes(keyword))) {
                debug('[转人工] 检测到投诉相关内容，触发转人工');
                return true;
            }
        }
    }

    return false;
}

/**
 * 查找转人工元素
 * @returns {Object} 转人工相关元素
 */
function findTransferElements() {
    try {
        // 查找转人工按钮
        const transferButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text === '转接' || text === '转人工' || text.includes('转接') || text.includes('转人工');
        });
        
        if (transferButtons.length > 0) {
            debug('[查找转人工] 找到转人工按钮:', transferButtons.length, '个');
            return { transferButton: transferButtons[0] };
        }
        
        // 查找更多菜单
        const moreButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text === '更多' || text.includes('更多');
        });
        
        if (moreButtons.length > 0) {
            debug('[查找转人工] 找到更多按钮:', moreButtons.length, '个');
            return { moreButton: moreButtons[0] };
        }
        
        debug('[查找转人工] 未找到转人工或更多按钮');
        return null;
    } catch (error) {
        debug('[查找转人工] 查找转人工元素时出错:', error);
        return null;
    }
}

/**
 * 查找转人工按钮
 * @returns {Promise<Element|null>} 转人工按钮元素
 */
async function findTransferButton() {
    try {
        debug('[查找转人工按钮] 开始查找转人工按钮');
        
        // 先直接查找转人工按钮
        let transferButton = document.querySelector('button.custom-btn-contained');
        
        // 如果找到了，检查文本内容
        if (transferButton) {
            const buttonText = transferButton.textContent.trim();
            if (buttonText === '转接' || buttonText.includes('转接') || buttonText.includes('转人工')) {
                debug('[查找转人工按钮] 直接找到转人工按钮:', buttonText);
                return transferButton;
            }
        }
        
        // 如果没找到，尝试点击更多按钮
        const moreButton = document.querySelector('button.operator-more-btn');
        if (moreButton) {
            debug('[查找转人工按钮] 找到更多按钮，准备点击');
            moreButton.click();
            await sleep(500);
            
            // 点击更多后，查找转人工按钮
            const menuItems = Array.from(document.querySelectorAll('.operator-more-menu li, .operator-more-menu-item'));
            
            // 寻找"转接"或"转人工"菜单项
            const transferItem = menuItems.find(item => {
                const text = item.textContent.trim();
                return text === '转接' || text.includes('转接') || text.includes('转人工');
            });
            
            if (transferItem) {
                debug('[查找转人工按钮] 在更多菜单中找到转人工按钮:', transferItem.textContent.trim());
                return transferItem;
            }
        }
        
        // 尝试从转人工元素中获取
        const elements = findTransferElements();
        if (elements && elements.transferButton) {
            debug('[查找转人工按钮] 通过findTransferElements找到转人工按钮');
            return elements.transferButton;
        }
        
        // 尝试查找可能的转人工按钮（通过常见类名和文本内容）
        const potentialButtons = Array.from(document.querySelectorAll('button, .btn, .button, li.menu-item, div[role="button"]'));
        
        const transferBtn = potentialButtons.find(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text === '转接' || text === '转人工' || text.includes('转接') || text.includes('转人工');
        });
        
        if (transferBtn) {
            debug('[查找转人工按钮] 通过潜在按钮查找到转人工按钮:', transferBtn.textContent.trim());
            return transferBtn;
        }
        
        debug('[查找转人工按钮] 未找到转人工按钮');
        return null;
    } catch (error) {
        debug('[查找转人工按钮] 查找转人工按钮时出错:', error);
        return null;
    }
}

/**
 * 选择目标客服
 * @param {string} selectedAgent 选定的客服账号
 * @returns {Promise<Element|null>} 目标客服元素
 */
async function selectTargetAgent(selectedAgent = null) {
    try {
        debug('[选择客服] 开始查找客服:', selectedAgent || '任意客服');
        
        // 等待客服列表加载
        await sleep(1000);
        
        // 查找客服列表
        const agentList = document.querySelector('.transfer-wrapper .scroll-content');
        if (!agentList) {
            debug('[选择客服] 未找到客服列表');
            return null;
        }
        
        // 获取所有客服元素
        const agentElements = Array.from(agentList.querySelectorAll('.transfer-item, .member-item'));
        
        if (agentElements.length === 0) {
            debug('[选择客服] 客服列表为空');
            return null;
        }
        
        debug('[选择客服] 找到', agentElements.length, '个客服');
        
        // 如果指定了客服账号，则查找匹配的客服
        if (selectedAgent) {
            debug('[选择客服] 尝试查找指定客服:', selectedAgent);
            
            // 查找名称完全匹配的客服
            let matchedAgent = agentElements.find(agent => {
                const nameElement = agent.querySelector('.member-name, .name');
                if (!nameElement) return false;
                
                const agentName = nameElement.textContent.trim();
                return agentName === selectedAgent;
            });
            
            // 如果没找到完全匹配的，尝试查找包含关键词的客服
            if (!matchedAgent) {
                matchedAgent = agentElements.find(agent => {
                    const nameElement = agent.querySelector('.member-name, .name');
                    if (!nameElement) return false;
                    
                    const agentName = nameElement.textContent.trim();
                    return agentName.includes(selectedAgent);
                });
            }
            
            if (matchedAgent) {
                debug('[选择客服] 找到匹配的客服:', matchedAgent.querySelector('.member-name, .name')?.textContent.trim());
                return matchedAgent;
            }
            
            debug('[选择客服] 未找到匹配的客服');
            return null;
        }
        
        // 如果没有指定客服账号，则随机选择一个在线的客服
        const onlineAgents = agentElements.filter(agent => {
            const statusElement = agent.querySelector('.status');
            return !statusElement || !statusElement.classList.contains('offline');
        });
        
        if (onlineAgents.length === 0) {
            debug('[选择客服] 没有在线的客服');
            return null;
        }
        
        // 随机选择一个在线客服
        const randomIndex = Math.floor(Math.random() * onlineAgents.length);
        const selectedElement = onlineAgents[randomIndex];
        
        debug('[选择客服] 随机选择了客服:', selectedElement.querySelector('.member-name, .name')?.textContent.trim());
        return selectedElement;
    } catch (error) {
        debug('[选择客服] 选择客服时出错:', error);
        return null;
    }
}

/**
 * 查找确认按钮
 * @returns {Promise<Element|null>} 确认按钮元素
 */
async function findConfirmButton() {
    try {
        // 等待按钮加载
        await sleep(500);
        
        // 查找常见的确认按钮类型
        const confirmButtons = Array.from(document.querySelectorAll('button, .btn, .button')).filter(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text === '确认' || text === '确定' || text === '是' || text === '确认转接' || text.includes('确认');
        });
        
        if (confirmButtons.length > 0) {
            debug('[查找确认按钮] 找到确认按钮:', confirmButtons[0].textContent.trim());
            return confirmButtons[0];
        }
        
        debug('[查找确认按钮] 未找到确认按钮');
        return null;
    } catch (error) {
        debug('[查找确认按钮] 查找确认按钮时出错:', error);
        return null;
    }
}

/**
 * 查找转移原因按钮
 * @returns {Promise<Element|null>} 转移原因按钮元素
 */
async function findTransferReasonButton() {
    try {
        // 等待按钮加载
        await sleep(500);
        
        // 首先查找在对话框中的转移原因按钮
        const reasonSelectors = [
            '.transfer-selector .transfer-selector-item', // 常见的转移原因选择器
            '.reason-item', // 可能的原因项
            '.transfer-reason-item', // 可能的转移原因项
            'li.menu-item', // 通用菜单项
            '.dropdown-item', // 下拉菜单项
        ];
        
        for (const selector of reasonSelectors) {
            const reasonElements = document.querySelectorAll(selector);
            
            if (reasonElements.length > 0) {
                // 优先选择"其他原因"
                for (const element of reasonElements) {
                    const text = element.textContent.trim();
                    if (text.includes('其他') || text.includes('其它') || text === '其他原因') {
                        debug('[查找转移原因] 找到"其他原因"按钮');
                        return element;
                    }
                }
                
                // 如果没有"其他原因"，选择第一个
                debug('[查找转移原因] 找到转移原因按钮:', reasonElements[0].textContent.trim());
                return reasonElements[0];
            }
        }
        
        // 如果没有找到特定的原因按钮，尝试查找通用确认按钮
        const confirmButton = await findConfirmButton();
        if (confirmButton) {
            return confirmButton;
        }
        
        debug('[查找转移原因] 未找到转移原因按钮');
        return null;
    } catch (error) {
        debug('[查找转移原因] 查找转移原因按钮时出错:', error);
        return null;
    }
}

/**
 * 查找取消收藏并转移按钮
 * @returns {Promise<Element|null>} 取消收藏并转移按钮元素
 */
async function findCancelStarButton() {
    try {
        // 等待按钮加载
        await sleep(500);
        
        // 查找可能的取消收藏并转移按钮
        const buttons = Array.from(document.querySelectorAll('button, .btn, .button'));
        
        // 优先查找"取消收藏并转移"按钮
        let cancelStarButton = buttons.find(btn => {
            const text = btn.textContent.trim();
            return text.includes('取消收藏并转移') || text.includes('取消收藏') || text.includes('继续转接');
        });
        
        if (cancelStarButton) {
            debug('[查找取消收藏按钮] 找到取消收藏按钮:', cancelStarButton.textContent.trim());
            return cancelStarButton;
        }
        
        // 如果没找到，查找通用的确认按钮（可能是弹窗中的确认按钮）
        const confirmButton = buttons.find(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text === '确认' || text === '确定' || text === '是' || text.includes('确认');
        });
        
        if (confirmButton) {
            debug('[查找取消收藏按钮] 找到确认按钮:', confirmButton.textContent.trim());
            return confirmButton;
        }
        
        // 如果还没找到，尝试查找对话框中的按钮
        const dialogButtons = document.querySelectorAll('.dialog-footer button, .modal-footer button');
        if (dialogButtons.length > 0) {
            // 通常，确认按钮是第一个或最后一个
            const confirmBtn = Array.from(dialogButtons).find(btn => {
                const text = btn.textContent.trim().toLowerCase();
                return text === '确认' || text === '确定' || text === '是' || text.includes('确认');
            }) || dialogButtons[dialogButtons.length - 1];
            
            debug('[查找取消收藏按钮] 在对话框中找到按钮:', confirmBtn.textContent.trim());
            return confirmBtn;
        }
        
        debug('[查找取消收藏按钮] 未找到取消收藏按钮或确认按钮');
        return null;
    } catch (error) {
        debug('[查找取消收藏按钮] 查找取消收藏按钮时出错:', error);
        return null;
    }
}

/**
 * 发送消息
 * @param {string} text 消息文本
 * @returns {Promise<boolean>} 是否发送成功
 */
async function sendMessage(text) {
    try {
        // 查找输入框
        const inputBox = document.querySelector('textarea.chat-input-textarea, textarea.chat-textarea');
        if (!inputBox) {
            debug('[发送消息] 未找到输入框');
            return false;
        }
        
        // 保存原始值
        const originalValue = inputBox.value;
        
        // 设置新值
        inputBox.value = text;
        
        // 触发输入事件
        const inputEvent = new Event('input', { bubbles: true });
        inputBox.dispatchEvent(inputEvent);
        
        // 等待一段时间确保值已更新
        await sleep(300);
        
        // 查找发送按钮
        const sendButton = document.querySelector('button.send-btn, button.chat-send-btn');
        if (!sendButton) {
            debug('[发送消息] 未找到发送按钮');
            // 恢复原始值
            inputBox.value = originalValue;
            inputBox.dispatchEvent(inputEvent);
            return false;
        }
        
        // 检查按钮是否可点击
        if (sendButton.disabled) {
            debug('[发送消息] 发送按钮被禁用');
            // 恢复原始值
            inputBox.value = originalValue;
            inputBox.dispatchEvent(inputEvent);
            return false;
        }
        
        // 点击发送按钮
        sendButton.click();
        
        debug('[发送消息] 消息已发送:', text);
        return true;
    } catch (error) {
        debug('[发送消息] 发送消息时出错:', error);
        return false;
    }
}

/**
 * 处理转人工关闭的情况
 * @returns {Promise<boolean>} 处理结果
 */
async function handleTransferClosed() {
    debug('[转人工关闭] 处理转人工关闭的情况');
    
    try {
        // 获取转人工关闭时的处理设置
        const settings = await StateManager.getState('transferSettings', {});
        const options = settings.transferFailOptions || ['message'];
        const message = settings.transferFailMessage || '实在抱歉无法转接，请稍候再联系';
        
        debug('[转人工关闭] 处理选项:', options.join(', '));
        
        // 发送提示消息
        if (options.includes('message')) {
            debug('[转人工关闭] 发送提示消息:', message);
            await sendMessage(message);
        }
        
        // 标记星标
        if (options.includes('star')) {
            debug('[转人工关闭] 准备标记星标');
            actionStarFlag();
        }
        
        return true;
    } catch (error) {
        debug('[转人工关闭] 处理转人工关闭时出错:', error);
        return false;
    }
}

/**
 * 处理转人工失败的情况
 * @returns {Promise<boolean>} 处理结果
 */
async function handleTransferFailed() {
    debug('[转人工失败] 处理转人工失败的情况');
    
    try {
        // 获取转人工失败时的处理设置
        const settings = await StateManager.getState('transferSettings', {});
        const options = settings.transferErrorOptions || ['message', 'star'];
        const message = settings.transferErrorMessage || '抱歉客服暂时离开，稍后为您处理';
        
        debug('[转人工失败] 处理选项:', options.join(', '));
        
        // 发送提示消息
        if (options.includes('message')) {
            debug('[转人工失败] 发送提示消息:', message);
            await sendMessage(message);
        }
        
        // 标记星标
        if (options.includes('star')) {
            debug('[转人工失败] 准备标记星标');
            actionStarFlag();
        }
        
        return true;
    } catch (error) {
        debug('[转人工失败] 处理转人工失败时出错:', error);
        return false;
    }
}

/**
 * 标记星标
 */
function actionStarFlag() {
    try {
        debug('[星标] 开始标记星标');
        
        // 查找星标按钮
        const starButton = document.querySelector('.icon-star, .star-icon, button.star-btn');
        
        if (starButton) {
            debug('[星标] 找到星标按钮，准备点击');
            starButton.click();
            return true;
        }
        
        // 如果没找到直接的星标按钮，尝试查找更多菜单
        const moreButton = document.querySelector('button.operator-more-btn, .more-btn');
        
        if (moreButton) {
            debug('[星标] 找到更多按钮，准备点击');
            moreButton.click();
            
            // 等待菜单出现
            setTimeout(() => {
                // 在菜单中查找星标选项
                const menuItems = document.querySelectorAll('.operator-more-menu li, .operator-more-menu-item, .dropdown-menu li');
                
                for (const item of menuItems) {
                    if (item.textContent.includes('星标') || item.textContent.includes('收藏')) {
                        debug('[星标] 在菜单中找到星标选项，准备点击');
                        item.click();
                        return true;
                    }
                }
                
                debug('[星标] 在菜单中未找到星标选项');
            }, 500);
        }
        
        debug('[星标] 未找到星标按钮或更多菜单');
        return false;
    } catch (error) {
        debug('[星标] 标记星标时出错:', error);
        return false;
    }
}

/**
 * 加载设置
 */
async function loadSettings() {
    try {
        debug('[设置] 开始加载设置...');
        
        // 获取所有设置元素
        const elements = {
            manualEnabled: document.getElementById('manualEnabled'),
            keywordEnabled: document.getElementById('keywordEnabled'),
            imageEnabled: document.getElementById('imageEnabled'),
            refundEnabled: document.getElementById('refundEnabled'),
            complaintEnabled: document.getElementById('complaintEnabled'),
            difyTransferEnabled: document.getElementById('difyTransferEnabled'),
            targetServiceAccount: document.getElementById('targetServiceAccount')
        };

        // 检查所有元素是否存在
        const missingElements = Object.entries(elements)
            .filter(([key, element]) => !element)
            .map(([key]) => key);

        if (missingElements.length > 0) {
            throw new Error(`找不到以下设置元素: ${missingElements.join(', ')}`);
        }

        // 从存储中获取设置
        const transferSettings = await StateManager.getState('transferSettings', {
            manualEnabled: true,
            keywordEnabled: true,
            imageEnabled: true,
            refundEnabled: true,
            complaintEnabled: true,
            difyTransferEnabled: true
        });

        // 设置界面元素值
        elements.manualEnabled.checked = transferSettings.manualEnabled;
        elements.keywordEnabled.checked = transferSettings.keywordEnabled;
        elements.imageEnabled.checked = transferSettings.imageEnabled;
        elements.refundEnabled.checked = transferSettings.refundEnabled;
        elements.complaintEnabled.checked = transferSettings.complaintEnabled;
        elements.difyTransferEnabled.checked = transferSettings.difyTransferEnabled || true;
        
        // 设置转人工失败处理选项
        const transferErrorOptions = transferSettings.transferErrorOptions || ['message', 'star'];
        const transferErrorOption1 = document.getElementById('transferErrorOption1');
        const transferErrorOption2 = document.getElementById('transferErrorOption2');
        
        if (transferErrorOption1) {
            transferErrorOption1.checked = transferErrorOptions.includes('message');
        }
        
        if (transferErrorOption2) {
            transferErrorOption2.checked = transferErrorOptions.includes('star');
        }
        
        // 设置转人工关闭处理选项
        const transferFailOptions = transferSettings.transferFailOptions || ['message'];
        const transferFailOption1 = document.getElementById('transferFailOption1');
        const transferFailOption2 = document.getElementById('transferFailOption2');
        
        if (transferFailOption1) {
            transferFailOption1.checked = transferFailOptions.includes('message');
        }
        
        if (transferFailOption2) {
            transferFailOption2.checked = transferFailOptions.includes('star');
        }
        
        // 设置提示消息
        const transferErrorMessage = document.getElementById('transferErrorMessage');
        if (transferErrorMessage) {
            transferErrorMessage.value = transferSettings.transferErrorMessage || '抱歉客服暂时离开，稍后为您处理';
        }
        
        const transferFailMessage = document.getElementById('transferFailMessage');
        if (transferFailMessage) {
            transferFailMessage.value = transferSettings.transferFailMessage || '实在抱歉无法转接，请稍候再联系';
        }

        debug('[设置] 加载设置完成');
    } catch (error) {
        debug('[设置] 加载设置时出错:', error);
    }
}

/**
 * 保存设置
 */
async function saveSettings() {
    try {
        debug('[设置] 开始保存设置...');
        
        // 获取所有设置元素
        const manualEnabled = document.getElementById('manualEnabled')?.checked;
        const keywordEnabled = document.getElementById('keywordEnabled')?.checked;
        const imageEnabled = document.getElementById('imageEnabled')?.checked;
        const refundEnabled = document.getElementById('refundEnabled')?.checked;
        const complaintEnabled = document.getElementById('complaintEnabled')?.checked;
        const difyTransferEnabled = document.getElementById('difyTransferEnabled')?.checked;
        
        // 获取转人工失败处理选项
        const transferErrorOption1 = document.getElementById('transferErrorOption1')?.checked;
        const transferErrorOption2 = document.getElementById('transferErrorOption2')?.checked;
        const transferErrorOptions = [
            transferErrorOption1 ? 'message' : '',
            transferErrorOption2 ? 'star' : ''
        ].filter(Boolean);
        
        // 获取转人工关闭处理选项
        const transferFailOption1 = document.getElementById('transferFailOption1')?.checked;
        const transferFailOption2 = document.getElementById('transferFailOption2')?.checked;
        const transferFailOptions = [
            transferFailOption1 ? 'message' : '',
            transferFailOption2 ? 'star' : ''
        ].filter(Boolean);
        
        // 获取提示消息
        const transferErrorMessage = document.getElementById('transferErrorMessage')?.value;
        const transferFailMessage = document.getElementById('transferFailMessage')?.value;
        
        // 获取指定的客服账号
        const targetServiceAccount = document.getElementById('targetServiceAccount')?.value.trim();
        const serviceAccountError = document.getElementById('targetServiceAccountError');
        
        // 获取之前的设置
        const oldSettings = await StateManager.getState('transferSettings', {});
        
        // 检查是否至少有一个服务账号
        const serviceAccounts = oldSettings.serviceAccounts || [];
        
        if (manualEnabled && serviceAccounts.length === 0 && !targetServiceAccount) {
            if (serviceAccountError) {
                serviceAccountError.style.display = 'block';
            }
            debug('[设置] 未设置服务账号，无法保存');
            return;
        }
        
        if (serviceAccountError) {
            serviceAccountError.style.display = 'none';
        }
        
        // 构建新的设置对象
        const settings = {
            ...oldSettings,
            manualEnabled,
            keywordEnabled,
            imageEnabled,
            refundEnabled,
            complaintEnabled,
            difyTransferEnabled,
            transferErrorOptions,
            transferFailOptions,
            transferErrorMessage,
            transferFailMessage
        };
        
        // 保存到Chrome存储
        await StateManager.setState('transferSettings', settings);
        
        // 显示保存成功提示
        const saveSuccess = document.getElementById('settingsSaveSuccess');
        if (saveSuccess) {
            saveSuccess.style.display = 'block';
            setTimeout(() => {
                saveSuccess.style.display = 'none';
            }, 2000);
        }
        
        debug('[设置] 保存设置完成');
    } catch (error) {
        debug('[设置] 保存设置时出错:', error);
    }
}

/**
 * 保存其他设置
 */
async function saveOtherSettings() {
    try {
        debug('[设置] 开始保存其他设置...');
        
        // 获取转人工失败提示消息
        const transferFailMessage = document.getElementById('transferFailMessage')?.value;
        
        if (transferFailMessage) {
            // 保存到Chrome存储
            chrome.storage.local.set({ transferFailMessage });
            
            // 同时更新transferSettings
            const settings = await StateManager.getState('transferSettings', {});
            await StateManager.setState('transferSettings', {
                ...settings,
                transferFailMessage
            });
            
            // 显示保存成功提示
            const saveSuccess = document.getElementById('transferFailMessageSaveSuccess');
            if (saveSuccess) {
                saveSuccess.style.display = 'block';
                saveSuccess.style.opacity = '1';
                
                setTimeout(() => {
                    saveSuccess.style.opacity = '0';
                    setTimeout(() => {
                        saveSuccess.style.display = 'none';
                    }, 300);
                }, 2000);
            }
        }
        
        debug('[设置] 保存其他设置完成');
    } catch (error) {
        debug('[设置] 保存其他设置时出错:', error);
    }
}

/**
 * 加载转人工关键词
 * @returns {Promise<string[]>} 关键词列表
 */
async function loadTransferKeywords() {
    try {
        debug('[关键词] 开始加载转人工关键词...');
        
        // 从Chrome存储中获取关键词
        const result = await StateManager.getState('transferKeywords', []);
        
        debug('[关键词] 加载到', result.length, '个关键词');
        return result;
    } catch (error) {
        debug('[关键词] 加载转人工关键词时出错:', error);
        return [];
    }
}

/**
 * 保存转人工关键词
 * @param {string[]} keywords 关键词列表
 */
async function saveTransferKeywords(keywords) {
    try {
        debug('[关键词] 开始保存转人工关键词...');
        
        // 保存到Chrome存储
        await StateManager.setState('transferKeywords', keywords);
        
        debug('[关键词] 保存转人工关键词完成');
    } catch (error) {
        debug('[关键词] 保存转人工关键词时出错:', error);
    }
}

/**
 * 添加转人工关键词
 */
async function addTransferKeyword() {
    try {
        debug('[关键词] 开始添加转人工关键词...');
        
        // 获取输入框元素
        const keywordInput = document.getElementById('newKeyword');
        if (!keywordInput) {
            throw new Error('找不到关键词输入框');
        }
        
        // 获取关键词
        const keyword = keywordInput.value.trim();
        if (!keyword) {
            debug('[关键词] 关键词为空，不添加');
            return;
        }
        
        // 加载现有关键词
        const keywords = await loadTransferKeywords();
        
        // 检查是否已存在
        if (keywords.includes(keyword)) {
            debug('[关键词] 关键词已存在:', keyword);
            return;
        }
        
        // 添加关键词
        keywords.push(keyword);
        
        // 保存关键词
        await saveTransferKeywords(keywords);
        
        // 清空输入框
        keywordInput.value = '';
        
        // 重新加载关键词列表
        loadKeywordList();
        
        debug('[关键词] 添加转人工关键词完成:', keyword);
    } catch (error) {
        debug('[关键词] 添加转人工关键词时出错:', error);
    }
}

/**
 * 移除转人工关键词
 * @param {string} keyword 关键词
 */
async function removeTransferKeyword(keyword) {
    try {
        debug('[关键词] 开始移除转人工关键词:', keyword);
        
        // 加载现有关键词
        const keywords = await loadTransferKeywords();
        
        // 移除关键词
        const newKeywords = keywords.filter(k => k !== keyword);
        
        // 保存关键词
        await saveTransferKeywords(newKeywords);
        
        // 重新加载关键词列表
        loadKeywordList();
        
        debug('[关键词] 移除转人工关键词完成:', keyword);
    } catch (error) {
        debug('[关键词] 移除转人工关键词时出错:', error);
    }
}

/**
 * 加载关键词列表UI
 */
async function loadKeywordList() {
    try {
        debug('[关键词] 开始加载关键词列表UI...');
        
        // 获取关键词列表容器
        const keywordListContainer = document.getElementById('keywordList');
        if (!keywordListContainer) {
            throw new Error('找不到关键词列表容器');
        }
        
        // 清空容器
        keywordListContainer.innerHTML = '';
        
        // 加载关键词
        const keywords = await loadTransferKeywords();
        
        if (keywords.length === 0) {
            debug('[关键词] 无关键词');
            const emptyTip = document.createElement('div');
            emptyTip.className = 'keyword-empty-tip';
            emptyTip.textContent = '暂无关键词，可添加常见的转人工关键词';
            keywordListContainer.appendChild(emptyTip);
            return;
        }
        
        // 创建关键词元素
        keywords.forEach(keyword => {
            const keywordItem = document.createElement('div');
            keywordItem.className = 'keyword-item';
            
            const keywordText = document.createElement('span');
            keywordText.textContent = keyword;
            
            const removeButton = document.createElement('button');
            removeButton.className = 'keyword-remove';
            removeButton.textContent = '×';
            removeButton.onclick = () => removeTransferKeyword(keyword);
            
            keywordItem.appendChild(keywordText);
            keywordItem.appendChild(removeButton);
            
            keywordListContainer.appendChild(keywordItem);
        });
        
        debug('[关键词] 加载关键词列表UI完成');
    } catch (error) {
        debug('[关键词] 加载关键词列表UI时出错:', error);
    }
}

/**
 * 加载服务账号
 * @returns {Promise<string[]>} 服务账号列表
 */
async function loadServiceAccounts() {
    try {
        debug('[服务账号] 开始加载服务账号...');
        
        // 从设置中获取服务账号
        const settings = await StateManager.getState('transferSettings', {});
        const serviceAccounts = settings.serviceAccounts || [];
        
        debug('[服务账号] 加载到', serviceAccounts.length, '个服务账号');
        return serviceAccounts;
    } catch (error) {
        debug('[服务账号] 加载服务账号时出错:', error);
        return [];
    }
}

/**
 * 保存服务账号
 * @param {string[]} accounts 服务账号列表
 */
async function saveServiceAccounts(accounts) {
    try {
        debug('[服务账号] 开始保存服务账号...');
        
        // 获取现有设置
        const settings = await StateManager.getState('transferSettings', {});
        
        // 更新服务账号
        await StateManager.setState('transferSettings', {
            ...settings,
            serviceAccounts: accounts
        });
        
        debug('[服务账号] 保存服务账号完成');
    } catch (error) {
        debug('[服务账号] 保存服务账号时出错:', error);
    }
}

/**
 * 添加服务账号
 */
async function addServiceAccount() {
    try {
        debug('[服务账号] 开始添加服务账号...');
        
        // 获取输入框元素
        const accountInput = document.getElementById('targetServiceAccount');
        if (!accountInput) {
            throw new Error('找不到服务账号输入框');
        }
        
        // 获取账号
        const account = accountInput.value.trim();
        if (!account) {
            debug('[服务账号] 账号为空，不添加');
            return;
        }
        
        // 加载现有账号
        const accounts = await loadServiceAccounts();
        
        // 检查是否已存在
        if (accounts.includes(account)) {
            debug('[服务账号] 账号已存在:', account);
            return;
        }
        
        // 添加账号
        accounts.push(account);
        
        // 保存账号
        await saveServiceAccounts(accounts);
        
        // 清空输入框
        accountInput.value = '';
        
        // 重新加载账号列表
        loadServiceAccountList();
        
        // 隐藏错误提示
        const error = document.getElementById('targetServiceAccountError');
        if (error) {
            error.style.display = 'none';
        }
        
        debug('[服务账号] 添加服务账号完成:', account);
    } catch (error) {
        debug('[服务账号] 添加服务账号时出错:', error);
    }
}

/**
 * 移除服务账号
 * @param {string} account 服务账号
 */
async function removeServiceAccount(account) {
    try {
        debug('[服务账号] 开始移除服务账号:', account);
        
        // 加载现有账号
        const accounts = await loadServiceAccounts();
        
        // 移除账号
        const newAccounts = accounts.filter(a => a !== account);
        
        // 保存账号
        await saveServiceAccounts(newAccounts);
        
        // 重新加载账号列表
        loadServiceAccountList();
        
        debug('[服务账号] 移除服务账号完成:', account);
    } catch (error) {
        debug('[服务账号] 移除服务账号时出错:', error);
    }
}

/**
 * 加载服务账号列表UI
 */
async function loadServiceAccountList() {
    try {
        debug('[服务账号] 开始加载服务账号列表UI...');
        
        // 获取账号列表容器
        const accountListContainer = document.getElementById('serviceAccountList');
        if (!accountListContainer) {
            throw new Error('找不到服务账号列表容器');
        }
        
        // 清空容器
        accountListContainer.innerHTML = '';
        
        // 加载账号
        const accounts = await loadServiceAccounts();
        
        if (accounts.length === 0) {
            debug('[服务账号] 无服务账号');
            const emptyTip = document.createElement('div');
            emptyTip.className = 'keyword-empty-tip';
            emptyTip.textContent = '暂无客服账号，请添加要转接的客服账号';
            accountListContainer.appendChild(emptyTip);
            return;
        }
        
        // 创建账号元素
        accounts.forEach(account => {
            const accountItem = document.createElement('div');
            accountItem.className = 'keyword-item';
            
            const accountText = document.createElement('span');
            accountText.textContent = account;
            
            const removeButton = document.createElement('button');
            removeButton.className = 'keyword-remove';
            removeButton.textContent = '×';
            removeButton.onclick = () => removeServiceAccount(account);
            
            accountItem.appendChild(accountText);
            accountItem.appendChild(removeButton);
            
            accountListContainer.appendChild(accountItem);
        });
        
        debug('[服务账号] 加载服务账号列表UI完成');
    } catch (error) {
        debug('[服务账号] 加载服务账号列表UI时出错:', error);
    }
}

/**
 * 初始化关键词
 */
async function initializeKeywords() {
    try {
        debug('[初始化] 开始初始化关键词...');
        
        // 检查是否已有关键词
        const keywords = await loadTransferKeywords();
        
        if (keywords.length === 0) {
            // 添加默认关键词
            const defaultKeywords = ['转人工', '转接', '人工客服', '不明白', '差评', '投诉'];
            await saveTransferKeywords(defaultKeywords);
            debug('[初始化] 添加默认关键词:', defaultKeywords.join(', '));
        }
        
        debug('[初始化] 初始化关键词完成');
    } catch (error) {
        debug('[初始化] 初始化关键词时出错:', error);
    }
}

// 导出核心函数
window.TransferManager = {
    TransferState,  // 导出转人工状态枚举
    MESSAGE_TYPES,  // 导出消息类型定义
    handleTransfer,
    checkShouldTransfer,
    findTransferButton,
    selectTargetAgent,
    findConfirmButton,
    findTransferReasonButton,
    findCancelStarButton,
    handleTransferClosed,
    handleTransferFailed,
    showSettingsWindow,
    loadSettings,
    saveSettings,
    saveOtherSettings,
    loadTransferKeywords,
    saveTransferKeywords,
    addTransferKeyword,
    removeTransferKeyword,
    loadKeywordList,
    loadServiceAccounts,
    saveServiceAccounts,
    addServiceAccount,
    removeServiceAccount,
    loadServiceAccountList,
    initializeKeywords
};