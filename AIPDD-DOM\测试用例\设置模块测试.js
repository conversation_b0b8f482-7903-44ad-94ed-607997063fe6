/**
 * 设置模块测试用例
 * 测试设置模块的加载、保存和事件通知功能
 */

// 测试配置
const TEST_CONFIG = {
  // 测试设置类型
  testType: 'testSettings',
  
  // 测试设置值
  testSettings: {
    enabled: true,
    value: 42,
    name: 'Test Setting',
    options: ['option1', 'option2']
  },
  
  // 测试更新值
  updatedSettings: {
    enabled: false,
    value: 100,
    name: 'Updated Setting',
    options: ['option3']
  },
  
  // 事件监听超时（毫秒）
  eventTimeout: 1000
};

/**
 * 延迟执行函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise<void>}
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试设置加载与保存
 */
async function testSettingsLoadSave() {
  console.log('开始测试设置加载与保存...');
  
  try {
    const Settings = window.AIPDD.Settings;
    if (!Settings) {
      throw new Error('设置模块未加载');
    }
    
    // 保存测试设置
    const saveResult = await Settings.save(TEST_CONFIG.testType, TEST_CONFIG.testSettings);
    if (saveResult) {
      console.log('✓ 设置保存成功');
    } else {
      throw new Error('设置保存失败');
    }
    
    // 加载设置
    const loadedSettings = await Settings.get(TEST_CONFIG.testType);
    console.log('加载的设置:', loadedSettings);
    
    // 验证设置一致性
    const isEqual = JSON.stringify(loadedSettings) === JSON.stringify(TEST_CONFIG.testSettings);
    if (isEqual) {
      console.log('✓ 设置加载成功，且与保存的设置一致');
    } else {
      throw new Error('设置不一致');
    }
    
    // 更新设置
    const updateResult = await Settings.save(TEST_CONFIG.testType, TEST_CONFIG.updatedSettings);
    if (updateResult) {
      console.log('✓ 设置更新成功');
    } else {
      throw new Error('设置更新失败');
    }
    
    // 加载更新后的设置
    const updatedLoadedSettings = await Settings.get(TEST_CONFIG.testType);
    
    // 验证更新后的设置一致性
    const isUpdatedEqual = JSON.stringify(updatedLoadedSettings) === JSON.stringify(TEST_CONFIG.updatedSettings);
    if (isUpdatedEqual) {
      console.log('✓ 更新后的设置加载成功，且与更新的设置一致');
    } else {
      throw new Error('更新后的设置不一致');
    }
    
    // 重置设置
    const resetResult = await Settings.reset(TEST_CONFIG.testType);
    if (resetResult) {
      console.log('✓ 设置重置成功');
    } else {
      throw new Error('设置重置失败');
    }
    
    // 清理测试数据
    // 由于已经重置，不需要额外清理
    
    return true;
  } catch (error) {
    console.error('❌ 设置加载与保存测试失败:', error);
    return false;
  }
}

/**
 * 测试设置类型
 */
async function testSettingsTypes() {
  console.log('开始测试设置类型...');
  
  try {
    const Settings = window.AIPDD.Settings;
    if (!Settings) {
      throw new Error('设置模块未加载');
    }
    
    // 检查设置类型是否存在
    if (!Settings.TYPES) {
      throw new Error('设置类型未定义');
    }
    
    console.log('可用的设置类型:', Settings.TYPES);
    
    // 测试每种设置类型
    const settingsTypes = Object.values(Settings.TYPES);
    if (settingsTypes.length === 0) {
      throw new Error('没有可用的设置类型');
    }
    
    let successCount = 0;
    
    for (const type of settingsTypes) {
      try {
        // 获取该类型的设置
        const settings = await Settings.get(type);
        console.log(`${type} 设置:`, settings);
        
        // 验证设置是否为对象
        if (settings && typeof settings === 'object') {
          successCount++;
        } else {
          console.warn(`⚠️ ${type} 设置不是有效对象`);
        }
      } catch (err) {
        console.warn(`⚠️ 获取 ${type} 设置失败:`, err);
      }
    }
    
    console.log(`✓ 成功测试了 ${successCount}/${settingsTypes.length} 个设置类型`);
    
    return successCount > 0;
  } catch (error) {
    console.error('❌ 设置类型测试失败:', error);
    return false;
  }
}

/**
 * 测试专用设置API
 */
async function testSpecializedSettingsAPI() {
  console.log('开始测试专用设置API...');
  
  try {
    const Settings = window.AIPDD.Settings;
    if (!Settings) {
      throw new Error('设置模块未加载');
    }
    
    // 测试转接设置API
    if (Settings.transfer) {
      // 获取当前设置
      const currentSettings = await Settings.transfer.get();
      console.log('当前转接设置:', currentSettings);
      
      // 保存测试设置
      const testTransferSettings = {
        ...currentSettings,
        testField: 'test_value'
      };
      
      const saveResult = await Settings.transfer.save(testTransferSettings);
      if (saveResult) {
        console.log('✓ 转接设置保存成功');
      } else {
        throw new Error('转接设置保存失败');
      }
      
      // 重新加载设置
      const loadedSettings = await Settings.transfer.get();
      
      // 验证测试字段
      if (loadedSettings.testField === 'test_value') {
        console.log('✓ 转接设置加载成功，且包含测试字段');
      } else {
        throw new Error('转接设置测试字段不一致');
      }
      
      // 重置设置
      await Settings.transfer.reset();
      console.log('✓ 转接设置重置成功');
    } else {
      console.log('⚠️ 转接设置API不可用，跳过测试');
    }
    
    // 测试AI设置API
    if (Settings.ai) {
      // 获取当前设置
      const currentSettings = await Settings.ai.get();
      console.log('当前AI设置:', currentSettings);
      
      // 保存测试设置
      const testAISettings = {
        ...currentSettings,
        testField: 'ai_test_value'
      };
      
      const saveResult = await Settings.ai.save(testAISettings);
      if (saveResult) {
        console.log('✓ AI设置保存成功');
      } else {
        throw new Error('AI设置保存失败');
      }
      
      // 重新加载设置
      const loadedSettings = await Settings.ai.get();
      
      // 验证测试字段
      if (loadedSettings.testField === 'ai_test_value') {
        console.log('✓ AI设置加载成功，且包含测试字段');
      } else {
        throw new Error('AI设置测试字段不一致');
      }
      
      // 重置设置
      await Settings.ai.reset();
      console.log('✓ AI设置重置成功');
    } else {
      console.log('⚠️ AI设置API不可用，跳过测试');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 专用设置API测试失败:', error);
    return false;
  }
}

/**
 * 测试设置变更事件通知
 */
async function testSettingsEvents() {
  console.log('开始测试设置变更事件通知...');
  
  try {
    const Settings = window.AIPDD.Settings;
    const EventBus = window.AIPDD.Core?.Utils?.EventBus;
    
    if (!Settings) {
      throw new Error('设置模块未加载');
    }
    
    if (!EventBus) {
      console.log('⚠️ 事件总线不可用，跳过事件测试');
      return true;
    }
    
    // 创建事件监听Promise
    const eventPromise = new Promise((resolve) => {
      // 监听设置变更事件
      const handler = (data) => {
        console.log('收到设置变更事件:', data);
        
        // 验证事件数据
        if (data && data.type === TEST_CONFIG.testType) {
          resolve(true);
        } else {
          resolve(false);
        }
        
        // 移除监听器
        EventBus.off('settings:updated', handler);
      };
      
      // 添加监听器
      EventBus.on('settings:updated', handler);
    });
    
    // 设置超时
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => resolve(false), TEST_CONFIG.eventTimeout);
    });
    
    // 保存设置以触发事件
    await Settings.save(TEST_CONFIG.testType, TEST_CONFIG.testSettings);
    
    // 等待事件或超时
    const eventReceived = await Promise.race([eventPromise, timeoutPromise]);
    
    if (eventReceived) {
      console.log('✓ 成功接收到设置变更事件');
    } else {
      throw new Error('未能接收到设置变更事件');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 设置变更事件测试失败:', error);
    return false;
  }
}

/**
 * 测试兼容性API
 */
async function testCompatibilityAPI() {
  console.log('开始测试兼容性API...');
  
  try {
    // 检查StateManager是否存在
    if (!window.StateManager) {
      console.log('⚠️ StateManager不存在，跳过兼容性测试');
      return true;
    }
    
    // 测试getState
    if (typeof window.StateManager.getState === 'function') {
      // 使用getState获取转接设置
      const transferSettings = await window.StateManager.getState('transferSettings', {});
      console.log('通过StateManager获取的转接设置:', transferSettings);
      
      if (transferSettings && typeof transferSettings === 'object') {
        console.log('✓ StateManager.getState兼容性测试成功');
      } else {
        throw new Error('StateManager.getState兼容性测试失败');
      }
    } else {
      console.log('⚠️ StateManager.getState不存在，跳过测试');
    }
    
    // 测试setState
    if (typeof window.StateManager.setState === 'function') {
      // 使用setState设置临时测试值
      const testValue = { testField: 'compatibility_test' };
      await window.StateManager.setState('tempTestKey', testValue);
      
      // 使用getState验证
      const retrievedValue = await window.StateManager.getState('tempTestKey');
      
      if (retrievedValue && retrievedValue.testField === 'compatibility_test') {
        console.log('✓ StateManager.setState兼容性测试成功');
      } else {
        throw new Error('StateManager.setState兼容性测试失败');
      }
    } else {
      console.log('⚠️ StateManager.setState不存在，跳过测试');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 兼容性API测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('===== 设置模块测试开始 =====');
  
  let passedCount = 0;
  let failedCount = 0;
  
  // 测试设置加载与保存
  if (await testSettingsLoadSave()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试设置类型
  if (await testSettingsTypes()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试专用设置API
  if (await testSpecializedSettingsAPI()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试设置变更事件通知
  if (await testSettingsEvents()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n');
  
  // 测试兼容性API
  if (await testCompatibilityAPI()) {
    passedCount++;
  } else {
    failedCount++;
  }
  
  console.log('\n===== 设置模块测试结束 =====');
  console.log(`通过: ${passedCount} 测试`);
  console.log(`失败: ${failedCount} 测试`);
  
  return {
    total: passedCount + failedCount,
    passed: passedCount,
    failed: failedCount,
    success: failedCount === 0
  };
}

// 导出测试函数
window.SettingsModuleTest = {
  runAllTests,
  testSettingsLoadSave,
  testSettingsTypes,
  testSpecializedSettingsAPI,
  testSettingsEvents,
  testCompatibilityAPI
}; 