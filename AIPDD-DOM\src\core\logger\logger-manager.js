/**
 * 日志管理器
 * 提供增强的日志记录功能，支持多级日志、远程日志上报等
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.Core = window.AIPDD.Core || {};

(function() {
    'use strict';
    
    // 依赖检查
    const Dependencies = {
        Storage: window.AIPDD.Core && window.AIPDD.Core.Storage,
        EventBus: window.AIPDD.Core && window.AIPDD.Core.Utils && window.AIPDD.Core.Utils.EventBus,
        Settings: window.AIPDD.Core && window.AIPDD.Core.Settings
    };
    
    // 日志级别定义
    const LogLevels = {
        TRACE: 0,
        DEBUG: 1,
        INFO: 2,
        WARN: 3,
        ERROR: 4,
        FATAL: 5,
        NONE: 6
    };
    
    // 默认配置
    const DEFAULT_CONFIG = {
        enabled: true,
        level: LogLevels.INFO,
        useColors: true,
        showTimestamp: true,
        showModule: true,
        maxLogEntries: 1000,
        remoteLogging: false,
        remoteLogLevel: LogLevels.ERROR,
        remoteLogUrl: '',
        persistLogs: false,
        persistLogLevel: LogLevels.ERROR
    };
    
    // 日志颜色样式
    const LOG_STYLES = {
        trace: 'color: #BDBDBD',
        debug: 'color: #9E9E9E',
        info: 'color: #2196F3',
        warn: 'color: #FF9800',
        error: 'color: #F44336; font-weight: bold',
        fatal: 'color: #B71C1C; font-weight: bold; font-size: 1.1em'
    };
    
    /**
     * 日志管理器类
     */
    class LoggerManager {
        /**
         * 构造函数
         */
        constructor() {
            // 初始化配置
            this.config = { ...DEFAULT_CONFIG };
            
            // 初始化日志缓冲区
            this.logBuffer = [];
            
            // 初始化远程日志队列
            this.remoteLogQueue = [];
            
            // 绑定方法
            this.configure = this.configure.bind(this);
            this.log = this.log.bind(this);
            this.trace = this.trace.bind(this);
            this.debug = this.debug.bind(this);
            this.info = this.info.bind(this);
            this.warn = this.warn.bind(this);
            this.error = this.error.bind(this);
            this.fatal = this.fatal.bind(this);
            this.getLogEntries = this.getLogEntries.bind(this);
            this.clearLogs = this.clearLogs.bind(this);
            this.exportLogs = this.exportLogs.bind(this);
            this.loadSettings = this.loadSettings.bind(this);
            
            // 注册事件监听
            this._registerEventListeners();
            
            console.log('[LoggerManager] 日志管理器已初始化');
        }
        
        /**
         * 注册事件监听器
         * @private
         */
        _registerEventListeners() {
            if (Dependencies.EventBus) {
                // 监听设置更新事件
                Dependencies.EventBus.on('settings:updated', (data) => {
                    if (data.type === 'loggerSettings') {
                        this.loadSettings();
                    }
                });
                
                // 监听错误事件
                Dependencies.EventBus.on('error', (data) => {
                    this.error(data.message, data.module || 'system', data.error);
                });
            }
        }
        
        /**
         * 初始化日志管理器
         * @returns {Promise<void>}
         */
        async init() {
            try {
                console.log('[LoggerManager] 初始化日志管理器');
                
                // 加载设置
                await this.loadSettings();
                
                // 加载持久化日志
                if (this.config.persistLogs) {
                    await this._loadPersistedLogs();
                }
                
                // 设置定期清理
                this._setupLogCleanup();
                
                // 设置远程日志上报
                if (this.config.remoteLogging) {
                    this._setupRemoteLogging();
                }
                
                console.log('[LoggerManager] 日志管理器初始化完成');
            } catch (error) {
                console.error('[LoggerManager] 初始化日志管理器失败:', error);
            }
        }
        
        /**
         * 加载日志设置
         * @returns {Promise<void>}
         */
        async loadSettings() {
            try {
                let settings = { ...DEFAULT_CONFIG };
                
                // 从设置模块加载
                if (Dependencies.Settings && Dependencies.Settings.SettingsManager) {
                    const loggerSettings = await Dependencies.Settings.get('loggerSettings');
                    if (loggerSettings) {
                        settings = { ...settings, ...loggerSettings };
                    }
                } else if (Dependencies.Storage) {
                    // 从存储模块加载
                    const storedSettings = await Dependencies.Storage.get('loggerSettings');
                    if (storedSettings) {
                        settings = { ...settings, ...storedSettings };
                    }
                }
                
                // 更新配置
                this.configure(settings);
                
                console.log('[LoggerManager] 日志设置已加载:', settings);
            } catch (error) {
                console.error('[LoggerManager] 加载日志设置失败:', error);
            }
        }
        
        /**
         * 保存日志设置
         * @param {Object} settings - 日志设置
         * @returns {Promise<boolean>}
         */
        async saveSettings(settings) {
            try {
                // 合并设置
                const newSettings = { ...this.config, ...settings };
                
                // 保存到设置模块
                if (Dependencies.Settings && Dependencies.Settings.SettingsManager) {
                    await Dependencies.Settings.save('loggerSettings', newSettings);
                } else if (Dependencies.Storage) {
                    // 保存到存储模块
                    await Dependencies.Storage.set('loggerSettings', newSettings);
                }
                
                // 更新配置
                this.configure(newSettings);
                
                // 触发设置更新事件
                if (Dependencies.EventBus) {
                    Dependencies.EventBus.emit('logger:settings:updated', { settings: newSettings });
                }
                
                console.log('[LoggerManager] 日志设置已保存:', newSettings);
                return true;
            } catch (error) {
                console.error('[LoggerManager] 保存日志设置失败:', error);
                return false;
            }
        }
        
        /**
         * 配置日志管理器
         * @param {Object} options - 配置选项
         */
        configure(options = {}) {
            // 更新配置
            Object.keys(options).forEach(key => {
                if (this.config.hasOwnProperty(key)) {
                    this.config[key] = options[key];
                }
            });
            
            // 重新设置远程日志上报
            if (options.remoteLogging !== undefined || options.remoteLogUrl !== undefined) {
                this._setupRemoteLogging();
            }
        }
        
        /**
         * 获取当前时间戳字符串
         * @returns {string} 格式化的时间戳
         * @private
         */
        _getTimestamp() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
            
            return `${hours}:${minutes}:${seconds}.${milliseconds}`;
        }
        
        /**
         * 获取日志级别的数值
         * @param {string} level - 日志级别
         * @returns {number} 级别数值
         * @private
         */
        _getLevelValue(level) {
            if (typeof level === 'number') {
                return level;
            }
            
            const levelName = String(level).toLowerCase();
            switch (levelName) {
                case 'trace': return LogLevels.TRACE;
                case 'debug': return LogLevels.DEBUG;
                case 'info': return LogLevels.INFO;
                case 'warn': return LogLevels.WARN;
                case 'error': return LogLevels.ERROR;
                case 'fatal': return LogLevels.FATAL;
                default: return LogLevels.INFO;
            }
        }
        
        /**
         * 获取日志级别的名称
         * @param {number} level - 日志级别值
         * @returns {string} 级别名称
         * @private
         */
        _getLevelName(level) {
            switch (level) {
                case LogLevels.TRACE: return 'TRACE';
                case LogLevels.DEBUG: return 'DEBUG';
                case LogLevels.INFO: return 'INFO';
                case LogLevels.WARN: return 'WARN';
                case LogLevels.ERROR: return 'ERROR';
                case LogLevels.FATAL: return 'FATAL';
                default: return 'INFO';
            }
        }
        
        /**
         * 记录日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {string|number} [level='info'] - 日志级别
         * @param {Error} [error=null] - 错误对象
         */
        log(message, module = 'general', level = 'info', error = null) {
            try {
                // 检查是否启用日志
                if (!this.config.enabled) return;
                
                // 获取日志级别值
                const levelValue = this._getLevelValue(level);
                const levelName = this._getLevelName(levelValue);
                
                // 检查日志级别
                if (levelValue < this._getLevelValue(this.config.level)) return;
                
                // 准备日志条目
                const timestamp = new Date();
                const formattedTimestamp = this._getTimestamp();
                
                // 构建日志条目
                const logEntry = {
                    timestamp,
                    formattedTimestamp,
                    level: levelValue,
                    levelName,
                    module,
                    message: this._formatMessage(message),
                    error: error ? this._formatError(error) : null
                };
                
                // 添加到日志缓冲区
                this._addToBuffer(logEntry);
                
                // 输出到控制台
                this._printToConsole(logEntry);
                
                // 检查是否需要持久化
                if (this.config.persistLogs && levelValue >= this._getLevelValue(this.config.persistLogLevel)) {
                    this._persistLog(logEntry);
                }
                
                // 检查是否需要远程上报
                if (this.config.remoteLogging && levelValue >= this._getLevelValue(this.config.remoteLogLevel)) {
                    this._queueForRemoteLogging(logEntry);
                }
                
                // 触发日志事件
                if (Dependencies.EventBus) {
                    Dependencies.EventBus.emit('logger:log', logEntry);
                }
            } catch (err) {
                // 避免无限递归
                console.error('[LoggerManager] 记录日志时出错:', err);
            }
        }
        
        /**
         * 格式化消息
         * @param {any} message - 消息对象
         * @returns {string} 格式化后的消息
         * @private
         */
        _formatMessage(message) {
            if (typeof message === 'string') {
                return message;
            }
            
            try {
                return JSON.stringify(message);
            } catch (e) {
                return String(message);
            }
        }
        
        /**
         * 格式化错误对象
         * @param {Error} error - 错误对象
         * @returns {Object} 格式化后的错误信息
         * @private
         */
        _formatError(error) {
            if (!error) return null;
            
            return {
                name: error.name || 'Error',
                message: error.message || '',
                stack: error.stack || '',
                code: error.code || undefined
            };
        }
        
        /**
         * 添加日志到缓冲区
         * @param {Object} logEntry - 日志条目
         * @private
         */
        _addToBuffer(logEntry) {
            // 添加到缓冲区
            this.logBuffer.push(logEntry);
            
            // 检查缓冲区大小
            if (this.logBuffer.length > this.config.maxLogEntries) {
                this.logBuffer.shift();
            }
        }
        
        /**
         * 输出日志到控制台
         * @param {Object} logEntry - 日志条目
         * @private
         */
        _printToConsole(logEntry) {
            // 准备日志前缀
            let prefix = '';
            
            // 添加时间戳
            if (this.config.showTimestamp) {
                prefix += `[${logEntry.formattedTimestamp}] `;
            }
            
            // 添加日志级别
            prefix += `[${logEntry.levelName}]`;
            
            // 添加模块名称
            if (this.config.showModule && logEntry.module) {
                prefix += ` [${logEntry.module}]`;
            }
            
            // 获取日志级别对应的样式
            const levelStyle = LOG_STYLES[logEntry.levelName.toLowerCase()] || '';
            
            // 输出日志
            if (this.config.useColors) {
                console.log(`%c${prefix}`, levelStyle, logEntry.message);
                
                // 如果有错误，输出错误堆栈
                if (logEntry.error) {
                    console.log(`%c${prefix} Error:`, levelStyle, logEntry.error.message);
                    console.log(`%c${prefix} Stack:`, levelStyle, logEntry.error.stack);
                }
            } else {
                console.log(prefix, logEntry.message);
                
                // 如果有错误，输出错误堆栈
                if (logEntry.error) {
                    console.log(prefix, 'Error:', logEntry.error.message);
                    console.log(prefix, 'Stack:', logEntry.error.stack);
                }
            }
        }
        
        /**
         * 持久化日志
         * @param {Object} logEntry - 日志条目
         * @private
         */
        async _persistLog(logEntry) {
            try {
                if (!Dependencies.Storage) return;
                
                // 获取现有持久化日志
                const persistedLogs = await Dependencies.Storage.get('persistedLogs', []);
                
                // 添加新日志
                persistedLogs.push(logEntry);
                
                // 限制日志数量
                if (persistedLogs.length > this.config.maxLogEntries) {
                    persistedLogs.splice(0, persistedLogs.length - this.config.maxLogEntries);
                }
                
                // 保存日志
                await Dependencies.Storage.set('persistedLogs', persistedLogs);
            } catch (error) {
                console.error('[LoggerManager] 持久化日志失败:', error);
            }
        }
        
        /**
         * 加载持久化日志
         * @private
         */
        async _loadPersistedLogs() {
            try {
                if (!Dependencies.Storage) return;
                
                // 获取持久化日志
                const persistedLogs = await Dependencies.Storage.get('persistedLogs', []);
                
                // 添加到缓冲区
                if (persistedLogs.length > 0) {
                    this.logBuffer = [...persistedLogs, ...this.logBuffer].slice(-this.config.maxLogEntries);
                    console.log(`[LoggerManager] 已加载 ${persistedLogs.length} 条持久化日志`);
                }
            } catch (error) {
                console.error('[LoggerManager] 加载持久化日志失败:', error);
            }
        }
        
        /**
         * 设置日志清理任务
         * @private
         */
        _setupLogCleanup() {
            // 每天清理一次日志
            setInterval(async () => {
                try {
                    if (!Dependencies.Storage) return;
                    
                    // 获取持久化日志
                    const persistedLogs = await Dependencies.Storage.get('persistedLogs', []);
                    
                    // 只保留最近的日志
                    if (persistedLogs.length > this.config.maxLogEntries) {
                        const newLogs = persistedLogs.slice(-this.config.maxLogEntries);
                        await Dependencies.Storage.set('persistedLogs', newLogs);
                        console.log(`[LoggerManager] 已清理 ${persistedLogs.length - newLogs.length} 条过期日志`);
                    }
                } catch (error) {
                    console.error('[LoggerManager] 清理日志失败:', error);
                }
            }, 24 * 60 * 60 * 1000); // 24小时
        }
        
        /**
         * 设置远程日志上报
         * @private
         */
        _setupRemoteLogging() {
            // 清除现有定时器
            if (this._remoteLoggingInterval) {
                clearInterval(this._remoteLoggingInterval);
                this._remoteLoggingInterval = null;
            }
            
            // 如果启用了远程日志上报
            if (this.config.remoteLogging && this.config.remoteLogUrl) {
                // 每分钟上报一次日志
                this._remoteLoggingInterval = setInterval(() => {
                    this._sendRemoteLogs();
                }, 60 * 1000); // 1分钟
            }
        }
        
        /**
         * 将日志加入远程上报队列
         * @param {Object} logEntry - 日志条目
         * @private
         */
        _queueForRemoteLogging(logEntry) {
            if (!this.config.remoteLogging || !this.config.remoteLogUrl) return;
            
            // 添加到远程日志队列
            this.remoteLogQueue.push(logEntry);
            
            // 如果队列超过10条，立即上报
            if (this.remoteLogQueue.length >= 10) {
                this._sendRemoteLogs();
            }
        }
        
        /**
         * 发送远程日志
         * @private
         */
        async _sendRemoteLogs() {
            try {
                // 如果队列为空，直接返回
                if (this.remoteLogQueue.length === 0) return;
                
                // 如果没有配置远程日志URL，直接返回
                if (!this.config.remoteLogUrl) return;
                
                // 获取要发送的日志
                const logsToSend = [...this.remoteLogQueue];
                this.remoteLogQueue = [];
                
                // 发送日志
                const response = await fetch(this.config.remoteLogUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        logs: logsToSend,
                        appVersion: chrome.runtime.getManifest().version,
                        timestamp: new Date().toISOString()
                    })
                });
                
                // 检查响应
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                
                console.log(`[LoggerManager] 已上报 ${logsToSend.length} 条日志到远程服务器`);
            } catch (error) {
                console.error('[LoggerManager] 上报远程日志失败:', error);
                
                // 将日志放回队列
                this.remoteLogQueue = [...this.remoteLogQueue, ...this.remoteLogQueue];
                
                // 限制队列大小
                if (this.remoteLogQueue.length > this.config.maxLogEntries) {
                    this.remoteLogQueue = this.remoteLogQueue.slice(-this.config.maxLogEntries);
                }
            }
        }
        
        /**
         * 获取日志条目
         * @param {Object} [options={}] - 过滤选项
         * @param {number} [options.level] - 最低日志级别
         * @param {string} [options.module] - 模块名称
         * @param {Date} [options.startTime] - 开始时间
         * @param {Date} [options.endTime] - 结束时间
         * @param {number} [options.limit] - 返回的最大条目数
         * @returns {Array<Object>} 日志条目数组
         */
        getLogEntries(options = {}) {
            let logs = [...this.logBuffer];
            
            // 按级别过滤
            if (options.level !== undefined) {
                const levelValue = this._getLevelValue(options.level);
                logs = logs.filter(log => log.level >= levelValue);
            }
            
            // 按模块过滤
            if (options.module) {
                logs = logs.filter(log => log.module === options.module);
            }
            
            // 按时间范围过滤
            if (options.startTime) {
                logs = logs.filter(log => log.timestamp >= options.startTime);
            }
            
            if (options.endTime) {
                logs = logs.filter(log => log.timestamp <= options.endTime);
            }
            
            // 限制返回数量
            if (options.limit) {
                logs = logs.slice(-options.limit);
            }
            
            return logs;
        }
        
        /**
         * 清除日志
         * @param {boolean} [persistedToo=false] - 是否也清除持久化日志
         */
        clearLogs(persistedToo = false) {
            // 清除内存中的日志
            this.logBuffer = [];
            
            // 清除持久化日志
            if (persistedToo && Dependencies.Storage) {
                Dependencies.Storage.set('persistedLogs', [])
                    .catch(error => console.error('[LoggerManager] 清除持久化日志失败:', error));
            }
            
            console.log('[LoggerManager] 日志已清除');
        }
        
        /**
         * 导出日志
         * @param {string} [format='json'] - 导出格式，支持 'json' 和 'csv'
         * @returns {string} 导出的日志内容
         */
        exportLogs(format = 'json') {
            try {
                const logs = this.getLogEntries();
                
                if (format === 'csv') {
                    // 导出为CSV
                    const headers = ['Timestamp', 'Level', 'Module', 'Message', 'Error'];
                    const rows = logs.map(log => [
                        log.formattedTimestamp,
                        log.levelName,
                        log.module,
                        log.message,
                        log.error ? log.error.message : ''
                    ]);
                    
                    const csvContent = [
                        headers.join(','),
                        ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
                    ].join('\n');
                    
                    return csvContent;
                } else {
                    // 导出为JSON
                    return JSON.stringify(logs, null, 2);
                }
            } catch (error) {
                console.error('[LoggerManager] 导出日志失败:', error);
                return '';
            }
        }
        
        /**
         * 跟踪级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        trace(message, module = 'general', error = null) {
            this.log(message, module, 'trace', error);
        }
        
        /**
         * 调试级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        debug(message, module = 'general', error = null) {
            this.log(message, module, 'debug', error);
        }
        
        /**
         * 信息级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        info(message, module = 'general', error = null) {
            this.log(message, module, 'info', error);
        }
        
        /**
         * 警告级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        warn(message, module = 'general', error = null) {
            this.log(message, module, 'warn', error);
        }
        
        /**
         * 错误级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        error(message, module = 'general', error = null) {
            this.log(message, module, 'error', error);
        }
        
        /**
         * 致命错误级别日志
         * @param {string|any} message - 日志消息
         * @param {string} [module='general'] - 模块名称
         * @param {Error} [error=null] - 错误对象
         */
        fatal(message, module = 'general', error = null) {
            this.log(message, module, 'fatal', error);
        }
    }
    
    // 创建单例实例
    const instance = new LoggerManager();
    
    // 导出到命名空间
    window.AIPDD.Core.Logger = {
        LoggerManager: instance,
        LogLevels
    };
    
    console.log('[LoggerManager] 日志管理器模块加载完成');
})(); 