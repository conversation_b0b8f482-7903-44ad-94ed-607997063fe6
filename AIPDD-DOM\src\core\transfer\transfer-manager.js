// transfer-manager.js
(function() {
    // 依赖
    const Settings = window.AIPDD.Settings;
    const TransferRules = window.AIPDD.Transfer.Rules;
    const TransferState = window.AIPDD.Transfer.State;
    
    // DOM操作工具函数
    function findElement(selector, parent = document) {
        return parent.querySelector(selector);
    }
    
    function findElements(selector, parent = document) {
        return Array.from(parent.querySelectorAll(selector));
    }
    
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 转人工管理器
    const TransferManager = {
        /**
         * 初始化转人工功能
         * @returns {Promise<boolean>} 是否成功
         */
        initialize: async function() {
            try {
                console.log('[TransferManager] 初始化转人工功能');
                
                // 检查是否需要初始化关键词
                await this.initializeKeywords();
                
                // 加载服务账号列表
                const settings = await Settings.getTransferSettings();
                if (!settings.serviceAccounts || settings.serviceAccounts.length === 0) {
                    // 如果没有服务账号，尝试从storage中加载
                    try {
                        const accounts = await Settings.getServiceAccounts();
                        if (accounts && accounts.length > 0) {
                            // 更新设置
                            await Settings.updateTransferSettings({
                                serviceAccounts: accounts
                            });
                        }
                    } catch (error) {
                        console.error('[TransferManager] 加载服务账号列表失败:', error);
                    }
                }
                
                console.log('[TransferManager] 初始化完成');
                return true;
            } catch (error) {
                console.error('[TransferManager] 初始化失败:', error);
                return false;
            }
        },
        
        /**
         * 初始化转人工关键词
         * @returns {Promise<boolean>} 是否成功
         */
        initializeKeywords: async function() {
            try {
                // 获取关键词列表
                const keywords = await Settings.getTransferKeywords();
                
                // 如果关键词列表为空，设置默认关键词
                if (!keywords || keywords.length === 0) {
                    const defaultKeywords = ['转人工', '转接', '人工客服', '不明白'];
                    await Settings.updateTransferKeywords(defaultKeywords);
                    this._transferKeywords = defaultKeywords;
                } else {
                    this._transferKeywords = keywords;
                }
                
                console.log('[TransferManager] 关键词列表初始化完成，共', this._transferKeywords.length, '个关键词');
                return true;
            } catch (error) {
                console.error('[TransferManager] 初始化关键词失败:', error);
                return false;
            }
        },
        
        /**
         * 检查是否需要转人工
         * @param {Object} message - 消息对象
         * @returns {Promise<Object>} 检查结果
         */
        shouldTransfer: async function(message) {
            return await TransferRules.shouldTransfer(message);
        },
        
        /**
         * 兼容API: checkShouldTransfer
         * @param {Object} message - 消息对象
         * @param {Object} state - 会话状态
         * @returns {Promise<Object>} 检查结果
         */
        checkShouldTransfer: async function(message, state) {
            return await this.shouldTransfer(message);
        },
        
        /**
         * 执行转人工操作
         * @param {string} conversationId - 会话ID
         * @returns {Promise<boolean>} 是否成功
         */
        executeTransfer: async function(conversationId) {
            try {
                console.log('[TransferManager] 开始执行转人工, 会话ID:', conversationId);
                
                // 最大重试次数
                const MAX_RETRIES = 3;
                let retryCount = 0;
                
                // 获取服务账号列表
                const settings = await Settings.getTransferSettings();
                let serviceAccounts = settings.serviceAccounts || [];
                
                // 复制一份账号列表用于尝试
                let availableAccounts = [...serviceAccounts];
                
                while (retryCount < MAX_RETRIES) {
                    try {
                        // 查找转移按钮并点击
                        const transferButton = await this._findTransferButton();
                        if (!transferButton) throw new Error('未找到转移按钮');
                        
                        transferButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await sleep(500);
                        transferButton.click();
                        await sleep(1500);
                        
                        // 选择目标客服
                        const targetAgent = await this._selectTargetAgent(availableAccounts);
                        if (!targetAgent) throw new Error('未找到目标客服');
                        
                        targetAgent.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await sleep(500);
                        targetAgent.click();
                        await sleep(1500);
                        
                        // 处理后续确认流程
                        await this._completeTransferProcess();
                        
                        // 发送成功提示
                        await this._sendMessage('收到，请您稍候一下···');
                        
                        // 更新会话状态
                        const state = window.conversationStates?.get?.(conversationId);
                        if (state) {
                            state.transferState = TransferState.TRANSFERRED;
                        }
                        
                        return true;
                    } catch (error) {
                        console.log('[TransferManager] 第', retryCount + 1, '次转人工尝试失败:', error);
                        retryCount++;
                        
                        if (retryCount >= MAX_RETRIES) {
                            // 更新设置，自动关闭转人工功能
                            await Settings.updateTransferSettings({
                                manualEnabled: false
                            });
                            
                            // 更新会话状态
                            const state = window.conversationStates?.get?.(conversationId);
                            if (state) {
                                state.transferState = TransferState.FAILED;
                            }
                            
                            return await this.handleTransferFailed();
                        }
                        
                        await sleep(1000 * retryCount);
                    }
                }
                
                return false;
            } catch (error) {
                console.error('[TransferManager] 转人工执行失败:', error);
                return false;
            }
        },
        
        /**
         * 兼容API: handleTransfer
         * @param {string} conversationId - 会话ID
         * @returns {Promise<boolean>} 是否成功
         */
        handleTransfer: async function(conversationId) {
            return await this.executeTransfer(conversationId);
        },
        
        /**
         * 处理转人工失败的情况
         * @returns {Promise<boolean>} 是否成功
         */
        handleTransferFailed: async function() {
            try {
                // 获取转人工失败时的处理设置
                const settings = await Settings.getTransferSettings();
                const options = settings.transferErrorOptions || ['message', 'star'];
                const message = settings.transferErrorMessage || '抱歉客服暂时离开，稍后为您处理';
                
                // 发送提示消息
                if (options.includes('message')) {
                    await this._sendMessage(message);
                }
                
                // 标记星标
                if (options.includes('star')) {
                    await this._actionStarFlag();
                }
                
                return true;
            } catch (error) {
                console.error('[TransferManager] 处理转人工失败情况出错:', error);
                return false;
            }
        },
        
        /**
         * 处理转人工功能关闭情况
         * @returns {Promise<boolean>} 是否成功
         */
        handleTransferClosed: async function() {
            try {
                // 获取转人工关闭时的处理设置
                const settings = await Settings.getTransferSettings();
                const options = settings.transferFailOptions || ['message'];
                const message = settings.transferFailMessage || '实在抱歉无法转接，请稍候再联系';
                
                // 发送提示消息
                if (options.includes('message')) {
                    await this._sendMessage(message);
                }
                
                // 标记星标
                if (options.includes('star')) {
                    await this._actionStarFlag();
                }
                
                return true;
            } catch (error) {
                console.error('[TransferManager] 处理转人工关闭情况出错:', error);
                return false;
            }
        },
        
        /**
         * 显示转人工设置
         */
        showSettings: function() {
            // 加载设置对话框模块
            if (window.AIPDD.UI.TransferSettings) {
                window.AIPDD.UI.TransferSettings.show();
            } else {
                // 动态加载设置对话框模块
                const script = document.createElement('script');
                script.src = chrome.runtime.getURL('src/ui/settings-dialog.js');
                script.onload = function() {
                    if (window.AIPDD.UI.TransferSettings) {
                        window.AIPDD.UI.TransferSettings.show();
                    }
                };
                document.head.appendChild(script);
            }
        },
        
        // 以下是私有方法，不暴露给外部
        
        /**
         * 查找转人工按钮
         * @returns {Promise<Element|null>} 转人工按钮
         * @private
         */
        _findTransferButton: async function() {
            // 各种可能的转人工按钮选择器
            const selectors = [
                'button.custom-btn-contained',
                '.operator-more-menu li:contains("转接")', 
                'button:contains("转人工")',
                'button:contains("转接")'
            ];
            
            // 尝试直接查找按钮
            for (const selector of selectors) {
                const button = findElement(selector);
                if (button && 
                    button.textContent.trim().match(/转人工|转接/)) {
                    return button;
                }
            }
            
            // 尝试点击更多按钮后查找
            const moreButton = findElement('button.operator-more-btn, .more-btn');
            if (moreButton) {
                moreButton.click();
                await sleep(500);
                
                // 查找菜单项
                const menuItems = findElements('.operator-more-menu li, .dropdown-menu li');
                for (const item of menuItems) {
                    if (item.textContent.trim().match(/转人工|转接/)) {
                        return item;
                    }
                }
            }
            
            return null;
        },
        
        /**
         * 选择目标客服
         * @param {Array<Object>} availableAccounts - 可用的客服账号列表
         * @returns {Promise<Element|null>} 选中的客服元素
         * @private
         */
        _selectTargetAgent: async function(availableAccounts) {
            try {
                // 等待客服列表加载
                await sleep(1000);
                
                // 尝试查找客服列表
                const agentListSelector = '.agent-transfer-list';
                const agentList = findElement(agentListSelector);
                
                if (!agentList) {
                    console.error('[TransferManager] 未找到客服列表');
                    return null;
                }
                
                // 查找所有可选择的客服
                const agentItems = findElements('.agent-item', agentList);
                
                if (!agentItems || agentItems.length === 0) {
                    console.error('[TransferManager] 客服列表为空');
                    return null;
                }
                
                console.log('[TransferManager] 找到', agentItems.length, '个客服选项');
                
                // 如果有指定的客服账号，则尝试查找匹配的客服
                if (availableAccounts && availableAccounts.length > 0) {
                    for (const account of availableAccounts) {
                        // 查找匹配的客服
                        const matchingAgent = agentItems.find(item => {
                            const nameElement = findElement('.agent-name', item);
                            return nameElement && nameElement.textContent.includes(account.name);
                        });
                        
                        if (matchingAgent) {
                            console.log('[TransferManager] 找到匹配的客服:', account.name);
                            return matchingAgent;
                        }
                    }
                }
                
                // 如果没有找到匹配的客服，则选择第一个在线的客服
                const onlineAgent = agentItems.find(item => {
                    const statusElement = findElement('.agent-status', item);
                    return statusElement && statusElement.textContent.includes('在线');
                });
                
                if (onlineAgent) {
                    console.log('[TransferManager] 选择在线客服');
                    return onlineAgent;
                }
                
                // 如果没有在线客服，则选择第一个客服
                console.log('[TransferManager] 没有在线客服，选择第一个客服');
                return agentItems[0];
            } catch (error) {
                console.error('[TransferManager] 选择客服时出错:', error);
                return null;
            }
        },
        
        /**
         * 完成转人工流程（处理确认按钮和原因选择）
         * @returns {Promise<boolean>} 是否成功完成
         * @private
         */
        _completeTransferProcess: async function() {
            try {
                // 等待转人工确认按钮出现
                await sleep(1000);
                
                // 查找并点击确认按钮
                const confirmButtonSelectors = [
                    '.confirm-btn',
                    '.dialog-footer button:last-child',
                    'button.primary',
                    'button.confirm',
                    'button:contains("确认")',
                    'button:contains("确定")'
                ];
                
                let confirmButton = null;
                
                for (const selector of confirmButtonSelectors) {
                    confirmButton = findElement(selector);
                    if (confirmButton) break;
                }
                
                if (confirmButton) {
                    console.log('[TransferManager] 找到确认按钮，点击确认');
                    confirmButton.click();
                    await sleep(1000);
                } else {
                    console.warn('[TransferManager] 未找到确认按钮');
                }
                
                // 处理可能出现的转人工原因选择
                const reasonButtonSelectors = [
                    'button:contains("转人工原因")',
                    '.transfer-reason button',
                    '.dialog-content button'
                ];
                
                let reasonButton = null;
                
                for (const selector of reasonButtonSelectors) {
                    reasonButton = findElement(selector);
                    if (reasonButton) break;
                }
                
                if (reasonButton) {
                    console.log('[TransferManager] 找到转人工原因按钮，点击选择');
                    reasonButton.click();
                    await sleep(500);
                    
                    // 选择第一个原因
                    const reasonItems = findElements('.reason-item');
                    if (reasonItems && reasonItems.length > 0) {
                        reasonItems[0].click();
                        await sleep(500);
                        
                        // 再次点击确认按钮
                        for (const selector of confirmButtonSelectors) {
                            confirmButton = findElement(selector);
                            if (confirmButton) {
                                confirmButton.click();
                                await sleep(1000);
                                break;
                            }
                        }
                    }
                }
                
                return true;
            } catch (error) {
                console.error('[TransferManager] 完成转人工流程时出错:', error);
                return false;
            }
        },
        
        /**
         * 查找转移原因按钮
         * @returns {Promise<Element|null>} 原因按钮
         * @private
         */
        _findReasonButton: async function() {
            const selectors = [
                '.transfer-selector .transfer-selector-item',
                '.reason-item:contains("其他")',
                'li.menu-item:contains("其他")',
                '.dialog-footer button:contains("确认")'
            ];
            
            for (const selector of selectors) {
                const button = findElement(selector);
                if (button) return button;
            }
            
            return null;
        },
        
        /**
         * 查找确认按钮
         * @returns {Promise<Element|null>} 确认按钮
         * @private
         */
        _findConfirmButton: async function() {
            const selectors = [
                '.dialog-footer button:contains("确认")',
                '.dialog-footer button:contains("确定")',
                'button:contains("取消收藏并转移")',
                'button:contains("继续转接")'
            ];
            
            for (const selector of selectors) {
                const button = findElement(selector);
                if (button) return button;
            }
            
            return null;
        },
        
        /**
         * 标记星标
         * @returns {Promise<boolean>} 是否成功
         * @private
         */
        _actionStarFlag: async function() {
            try {
                // 查找星标按钮
                const starButtonSelectors = [
                    '.star-btn',
                    'button:contains("星标")',
                    '.header-actions button:first-child',
                    '.conversation-header .star-icon'
                ];
                
                let starButton = null;
                
                for (const selector of starButtonSelectors) {
                    starButton = findElement(selector);
                    if (starButton) break;
                }
                
                if (!starButton) {
                    console.error('[TransferManager] 未找到星标按钮');
                    return false;
                }
                
                // 检查是否已经是星标
                const isStarred = starButton.classList.contains('starred') || 
                                 starButton.classList.contains('active');
                
                if (!isStarred) {
                    console.log('[TransferManager] 点击星标按钮');
                    starButton.click();
                    await sleep(500);
                } else {
                    console.log('[TransferManager] 已经是星标状态');
                }
                
                return true;
            } catch (error) {
                console.error('[TransferManager] 标记星标时出错:', error);
                return false;
            }
        },
        
        /**
         * 发送消息
         * @param {string} text - 消息内容
         * @returns {Promise<boolean>} 是否成功
         * @private
         */
        _sendMessage: async function(text) {
            try {
                // 查找输入框
                const inputSelector = '.chat-editor';
                const input = findElement(inputSelector);
                
                if (!input) {
                    console.error('[TransferManager] 未找到输入框');
                    return false;
                }
                
                // 设置输入内容
                input.value = text;
                input.dispatchEvent(new Event('input', { bubbles: true }));
                
                // 查找发送按钮
                const sendButtonSelector = '.send-btn';
                const sendButton = findElement(sendButtonSelector);
                
                if (!sendButton) {
                    console.error('[TransferManager] 未找到发送按钮');
                    return false;
                }
                
                // 点击发送
                sendButton.click();
                
                return true;
            } catch (error) {
                console.error('[TransferManager] 发送消息时出错:', error);
                return false;
            }
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Transfer.Manager = TransferManager;
    
    // 将转人工状态常量直接附加到TransferManager对象上
    TransferManager.TransferState = TransferState;
    
    // 为了向下兼容，将Manager实例也直接暴露到window对象
    window.TransferManager = TransferManager;
})();