 /**
 * 转接管理模块 - 处理转人工相关功能
 */
(function(global) {
    'use strict';

    // 导入依赖
    const { debug } = global.AIPDD.Core.Utils.Debug;
    const { EventBus } = global.AIPDD.Core.Utils.EventBus;
    const { StorageManager } = global.AIPDD.Core.Storage;

    // 转接状态枚举
    const TransferState = {
        NONE: 0,
        PENDING: 1,
        TRANSFERRED: 2,
        FAILED: 3,
        CLOSED: 4
    };

    /**
     * 转接管理器类
     */
    class TransferManager {
        constructor() {
            this.eventBus = EventBus.getInstance();
            this.initEventListeners();
        }

        /**
         * 初始化事件监听
         */
        initEventListeners() {
            this.eventBus.on('transfer:request', (conversationId) => {
                this.handleTransfer(conversationId);
            });
        }

        /**
         * 处理转人工请求
         * @param {string} conversationId - 会话ID
         * @returns {Promise<boolean>} 是否成功
         */
        async handleTransfer(conversationId) {
            debug('[转人工] 开始处理转人工请求:', conversationId);
            
            // 最大重试次数
            const MAX_RETRIES = 3;
            let retryCount = 0;
            
            // 获取设置中指定的客服账号列表
            const settings = await StorageManager.get('transferSettings', {});
            let serviceAccounts = settings.serviceAccounts || [];
            
            // 兼容旧版本，如果没有serviceAccounts但有specifiedAgent，则使用specifiedAgent
            if (serviceAccounts.length === 0 && settings.specifiedAgent) {
                serviceAccounts = [settings.specifiedAgent.trim()];
            }
            
            // 复制一份账号列表，用于随机选择并移除已尝试过的账号
            let availableAccounts = [...serviceAccounts];
            
            while (retryCount < MAX_RETRIES) {
                try {
                    // 查找转移按钮
                    const transferButton = await this._findTransferButton();
                    if (!transferButton) {
                        throw new Error('未找到转移按钮');
                    }
                    
                    debug('[转人工] 找到转移按钮，准备点击');
                    // 确保按钮在视图中
                    transferButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this._sleep(500);
                    
                    // 模拟真实点击
                    const rect = transferButton.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        clientX: rect.left + rect.width / 2,
                        clientY: rect.top + rect.height / 2
                    });
                    transferButton.dispatchEvent(clickEvent);
                    await this._sleep(2000);
                    
                    // 选择目标客服
                    // 不再在selectTargetAgent中随机选择客服，而是在这里循环尝试所有可用的客服账号
                    let targetAgent = null;
                    let accountTrials = 0;
                    
                    // 尝试可用的客服账号，直到找到一个或者全部尝试失败
                    while (!targetAgent && availableAccounts.length > 0 && accountTrials < availableAccounts.length) {
                        // 随机选择一个客服账号
                        const randomIndex = Math.floor(Math.random() * availableAccounts.length);
                        const selectedAccount = availableAccounts[randomIndex];
                        
                        // 从可用账号列表中移除，避免重复尝试
                        availableAccounts.splice(randomIndex, 1);
                        
                        debug('[转人工] 尝试查找客服账号:', selectedAccount, '(第', accountTrials + 1, '次尝试)');
                        
                        // 尝试查找这个客服账号
                        targetAgent = await this._selectTargetAgent(selectedAccount);
                        accountTrials++;
                        
                        if (!targetAgent) {
                            debug('[转人工] 未找到客服账号:', selectedAccount);
                        }
                    }
                    
                    if (!targetAgent) {
                        // 本次重试所有账号都尝试完毕但未找到客服
                        throw new Error('未找到目标客服');
                    }
                    
                    debug('[转人工] 找到目标客服，准备点击');
                    targetAgent.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this._sleep(500);
                    targetAgent.click();
                    await this._sleep(2000);
                    
                    // 查找并点击转移原因按钮
                    const confirmButton = await this._findTransferReasonButton();
                    if (!confirmButton) {
                        throw new Error('未找到转移原因按钮');
                    }
                    
                    debug('[转人工] 找到转移原因按钮，准备点击');
                    confirmButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this._sleep(500);
                    confirmButton.click();
                    await this._sleep(2000);
                    
                    // 检查是否出现"取消收藏并转移"按钮或意见反馈对话框
                    const cancelStarButton = await this._findCancelStarButton();
                    if (cancelStarButton) {
                        debug('[转人工] 找到后续操作按钮，准备点击:', cancelStarButton.textContent);
                        cancelStarButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await this._sleep(500);
                        cancelStarButton.click();
                        await this._sleep(2000);
                    }
                    
                    // 更新状态为已转人工
                    this.eventBus.emit('conversation:updateState', {
                        id: conversationId,
                        transferState: TransferState.TRANSFERRED
                    });

                    // 发送转人工成功提示
                    await this._sendMessage('收到，请您稍候一下···');
                    
                    debug('[转人工] 转人工成功');
                    
                    // 不再刷新页面，直接返回成功
                    return true;
                    
                } catch (error) {
                    debug('[转人工] 第', retryCount + 1, '次尝试失败:', error);
                    retryCount++;
                    
                    // 如果还有账号可以尝试，继续尝试
                    if (availableAccounts.length > 0) {
                        debug('[转人工] 仍有', availableAccounts.length, '个客服账号可尝试');
                    } else {
                        // 重新填充账号列表以便下一次外层重试可以再次尝试所有账号
                        availableAccounts = [...serviceAccounts];
                        debug('[转人工] 所有客服账号都已尝试，将在下一次重试中重新尝试所有账号');
                    }
                    
                    if (retryCount >= MAX_RETRIES) {
                        debug('[转人工] 达到最大重试次数');
                        // 更新状态为失败
                        this.eventBus.emit('conversation:updateState', {
                            id: conversationId,
                            transferState: TransferState.FAILED
                        });
                        
                        // 自动关闭"自动转人工总开关"
                        debug('[转人工] 所有客服账号尝试失败，自动关闭"自动转人工总开关"');
                        const currentSettings = await StorageManager.get('transferSettings', {});
                        
                        // 更新设置，关闭自动转人工总开关
                        await StorageManager.set('transferSettings', {
                            ...currentSettings,
                            manualEnabled: false
                        });
                        
                        return await this.handleTransferFailed();
                    }
                    
                    // 等待一段时间后重试
                    await this._sleep(2000 * retryCount);
                }
            }
            
            return false;
        }

        /**
         * 处理转人工关闭
         * @returns {Promise<boolean>} 是否成功处理
         */
        async handleTransferClosed() {
            debug('[转人工] 处理转人工关闭');
            
            try {
                // 获取转人工关闭提示文案
                const settings = await StorageManager.get('transferSettings', {});
                const transferClosedMessage = settings.transferClosedMessage || '客服已关闭转接，请问还有其他问题吗？';
                
                // 发送提示消息
                await this._sendMessage(transferClosedMessage);
                
                return true;
            } catch (error) {
                debug('[转人工] 处理转人工关闭失败:', error);
                return false;
            }
        }

        /**
         * 处理转人工失败
         * @returns {Promise<boolean>} 是否成功处理
         */
        async handleTransferFailed() {
            debug('[转人工] 处理转人工失败');
            
            try {
                // 获取转人工失败提示文案
                const settings = await StorageManager.get('transferSettings', {});
                const transferFailedMessage = settings.transferFailedMessage || '抱歉，转人工失败，请您稍后再试';
                
                // 发送提示消息
                await this._sendMessage(transferFailedMessage);
                
                return true;
            } catch (error) {
                debug('[转人工] 处理转人工失败:', error);
                return false;
            }
        }

        // 以下是辅助方法，实际实现将调用平台接口

        /**
         * 查找转移按钮
         * @private
         */
        async _findTransferButton() {
            // 临时实现，后续会替换为平台接口
            if (typeof findTransferButton === 'function') {
                return await findTransferButton();
            }
            return null;
        }

        /**
         * 选择目标客服
         * @private
         */
        async _selectTargetAgent(selectedAgent) {
            // 临时实现，后续会替换为平台接口
            if (typeof selectTargetAgent === 'function') {
                return await selectTargetAgent(selectedAgent);
            }
            return null;
        }

        /**
         * 查找转移原因按钮
         * @private
         */
        async _findTransferReasonButton() {
            // 临时实现，后续会替换为平台接口
            if (typeof findTransferReasonButton === 'function') {
                return await findTransferReasonButton();
            }
            return null;
        }

        /**
         * 查找取消星标按钮
         * @private
         */
        async _findCancelStarButton() {
            // 临时实现，后续会替换为平台接口
            if (typeof findCancelStarButton === 'function') {
                return await findCancelStarButton();
            }
            return null;
        }

        /**
         * 发送消息
         * @private
         */
        async _sendMessage(text) {
            // 临时实现，后续会替换为平台接口
            if (typeof sendMessage === 'function') {
                return await sendMessage(text);
            }
            return false;
        }

        /**
         * 延迟执行
         * @private
         */
        async _sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // 创建单例实例
    const transferManager = new TransferManager();

    // 导出模块
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Core) global.AIPDD.Core = {};
    if (!global.AIPDD.Core.Transfer) global.AIPDD.Core.Transfer = {};
    
    global.AIPDD.Core.Transfer.TransferManager = transferManager;
    global.AIPDD.Core.Transfer.TransferState = TransferState;

})(window);