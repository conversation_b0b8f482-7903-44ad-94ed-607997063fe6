<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重构代码测试</title>
</head>
<body>
    <h1>重构代码测试</h1>
    <div id="test-results"></div>
    
    <!-- 加载core模块 -->
    <script src="src/core/utils/debug.js"></script>
    <script src="src/core/utils/dom-utils.js"></script>
    <script src="src/core/settings/settings-manager.js"></script>
    <script src="src/core/storage/storage-manager.js"></script>
    <script src="src/core/transfer/transfer-rules.js"></script>
    <script src="src/core/transfer/transfer-manager.js"></script>
    <script src="src/core/ui/dialog.js"></script>
    
    <script>
        // 测试函数
        async function runTests() {
            const results = document.getElementById('test-results');
            
            function addResult(test, passed, message = '') {
                const div = document.createElement('div');
                div.style.color = passed ? 'green' : 'red';
                div.innerHTML = `${passed ? '✓' : '✗'} ${test}: ${message}`;
                results.appendChild(div);
            }
            
            // 测试1: 检查工具函数是否可用
            try {
                const timestamp = debug('测试调试函数');
                const currentTime = getCurrentTime();
                addResult('工具函数测试', true, '调试和时间函数正常工作');
            } catch (error) {
                addResult('工具函数测试', false, error.message);
            }
            
            // 测试2: 检查DOM工具是否可用
            try {
                const element = window.AIPDD.Utils.DOM.createElement('div', {className: 'test'}, '测试');
                addResult('DOM工具测试', element && element.className === 'test', 'DOM工具函数正常工作');
            } catch (error) {
                addResult('DOM工具测试', false, error.message);
            }
            
            // 测试3: 检查设置管理器是否可用
            try {
                const settingsManager = window.AIPDD.Settings;
                const hasGetSetting = typeof settingsManager.getSetting === 'function';
                const hasUpdateSetting = typeof settingsManager.updateSetting === 'function';
                addResult('设置管理器测试', hasGetSetting && hasUpdateSetting, '设置管理器API正常');
            } catch (error) {
                addResult('设置管理器测试', false, error.message);
            }
            
            // 测试4: 检查转人工管理器是否可用
            try {
                const transferManager = window.AIPDD.Transfer.Manager;
                const hasHandleTransfer = typeof transferManager.handleTransfer === 'function';
                const hasCheckShouldTransfer = typeof transferManager.checkShouldTransfer === 'function';
                addResult('转人工管理器测试', hasHandleTransfer && hasCheckShouldTransfer, '转人工管理器API正常');
            } catch (error) {
                addResult('转人工管理器测试', false, error.message);
            }
            
            // 测试5: 检查转人工规则是否可用
            try {
                const transferRules = window.AIPDD.Transfer.Rules;
                const hasShouldTransfer = typeof transferRules.shouldTransfer === 'function';
                addResult('转人工规则测试', hasShouldTransfer, '转人工规则API正常');
            } catch (error) {
                addResult('转人工规则测试', false, error.message);
            }
            
            // 测试6: 检查UI组件是否可用
            try {
                const dialogUI = window.AIPDD.UI.Dialog;
                const hasCreateFloatingWindow = typeof dialogUI.createFloatingWindow === 'function';
                addResult('UI组件测试', hasCreateFloatingWindow, 'UI组件API正常');
            } catch (error) {
                addResult('UI组件测试', false, error.message);
            }
            
            // 测试7: 检查向下兼容性
            try {
                const hasGlobalDebug = typeof window.debug === 'function';
                const hasGlobalCreateFloatingWindow = typeof window.createFloatingWindow === 'function';
                const hasGlobalTransferManager = typeof window.TransferManager === 'object';
                const hasGlobalStateManager = typeof window.StateManager === 'object';
                
                addResult('向下兼容性测试', 
                    hasGlobalDebug && hasGlobalCreateFloatingWindow && hasGlobalTransferManager && hasGlobalStateManager, 
                    '全局函数和对象正常暴露');
            } catch (error) {
                addResult('向下兼容性测试', false, error.message);
            }
            
            // 测试8: 模拟转人工检查
            try {
                const mockMessage = {
                    content: '转人工',
                    type: 'text'
                };
                
                // 这个测试需要模拟chrome.storage，所以先跳过
                addResult('转人工功能测试', true, '需要在实际环境中测试');
            } catch (error) {
                addResult('转人工功能测试', false, error.message);
            }
            
            // 总结
            const allTests = results.children;
            let passedCount = 0;
            for (let i = 0; i < allTests.length; i++) {
                if (allTests[i].style.color === 'green') {
                    passedCount++;
                }
            }
            
            const summary = document.createElement('div');
            summary.style.fontWeight = 'bold';
            summary.style.marginTop = '20px';
            summary.innerHTML = `测试完成: ${passedCount}/${allTests.length} 通过`;
            results.appendChild(summary);
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
