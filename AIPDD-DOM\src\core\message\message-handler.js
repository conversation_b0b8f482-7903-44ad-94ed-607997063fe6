/**
 * 消息处理器
 * 负责处理消息的核心逻辑，包括消息的接收、处理和回复
 */

// 使用AIPDD命名空间
window.AIPDD = window.AIPDD || {};
window.AIPDD.MessageHandler = (function() {
    'use strict';
    
    // 私有变量
    const debug = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.Debug ? 
        window.AIPDD.Utils.Debug.log : 
        console.log.bind(console, '[MessageHandler]');
    
    // 引用消息类型常量
    const MESSAGE_TYPES = window.AIPDD.MessageTypes || {
        TEXT: 'text',
        IMAGE: 'image',
        PRODUCT: 'product',
        ORDER: 'order',
        REFUND: 'refund',
        LEGOCARD: 'legocard',
        UNKNOWN: 'unknown'
    };
    
    // 引用消息解析器
    const MessageParser = window.AIPDD.MessageParser;
    
    // 引用事件总线
    const EventBus = window.AIPDD && window.AIPDD.Utils && window.AIPDD.Utils.EventBus;
    
    // 引用转接管理器
    const TransferManager = window.AIPDD && window.AIPDD.Core && window.AIPDD.Core.Transfer;
    
    // 引用回复处理器
    const ReplyProcessor = window.AIPDD && window.AIPDD.Modules && window.AIPDD.Modules.Reply;
    
    // 处理中的消息标记
    let isProcessingMessage = false;
    let lastMessageProcessingStartTime = 0;
    
    // 会话状态集合
    const conversationStates = new Map();
    
    /**
     * 会话状态类
     */
    class ConversationState {
        constructor(pddId, nickname, user_id='') {
            this.pddConversationId = pddId;
            this.userName = nickname;
            this.userId = user_id;
            this.lastProcessedTime = 0;
            this.processedMessages = new Set();
            this.transferState = {
                inProgress: false,
                lastAttempt: 0,
                attempts: 0,
                success: false
            };
        }
        
        updateUser(nickname, user_id='') {
            this.userName = nickname;
            this.userId = user_id;
        }
        
        getUserName() {
            return this.userName;
        }
        
        getPddConversationId() {
            return this.pddConversationId;
        }
        
        resetTransferState() {
            this.transferState = {
                inProgress: false,
                lastAttempt: 0,
                attempts: 0,
                success: false
            };
        }
        
        canTransfer() {
            // 如果转人工成功，不再允许转人工
            if (this.transferState.success) {
                return false;
            }
            
            // 如果正在转人工，不允许再次转人工
            if (this.transferState.inProgress) {
                return false;
            }
            
            // 如果尝试次数超过3次，不允许再次转人工
            if (this.transferState.attempts >= 3) {
                return false;
            }
            
            // 如果上次尝试时间在5分钟内，不允许再次转人工
            const now = Date.now();
            const fiveMinutes = 5 * 60 * 1000;
            if (now - this.transferState.lastAttempt < fiveMinutes) {
                return false;
            }
            
            return true;
        }
        
        // 检查是否在指定时间内处理过消息
        hasProcessed(conversationId, minutes) {
            if (!this.lastProcessedTime) return false;
            const now = Date.now();
            const timeWindow = minutes * 60 * 1000;
            return (now - this.lastProcessedTime) < timeWindow;
        }
        
        // 标记已处理
        markProcessed(conversationId, minutes) {
            this.lastProcessedTime = Date.now();
        }
    }
    
    /**
     * 初始化会话状态
     * @param {string} conversationId - 会话ID
     * @param {string} nickname - 用户昵称
     * @param {string} userId - 用户ID
     */
    function initConversationState(conversationId, nickname, userId = '') {
        if (!conversationStates.has(conversationId)) {
            conversationStates.set(conversationId, new ConversationState(conversationId, nickname, userId));
            debug(`初始化会话状态: ${conversationId}, 用户: ${nickname}`);
        } else {
            // 更新用户信息
            const state = conversationStates.get(conversationId);
            state.updateUser(nickname, userId);
            debug(`更新会话状态: ${conversationId}, 用户: ${nickname}`);
        }
    }
    
    /**
     * 获取会话状态
     * @param {string} conversationId - 会话ID
     * @returns {ConversationState|null} - 会话状态对象
     */
    function getConversationState(conversationId) {
        return conversationStates.get(conversationId) || null;
    }
    
    /**
     * 处理单条消息
     * @param {Object} message - 消息对象
     * @param {string} conversationId - 会话ID
     */
    async function processMessage(message, conversationId) {
        debug('[监听消息]开始处理消息:', message);
        try {
            // 检查自动回复状态
            if (!window.floatingBall?.autoReplyEnabled) {
                debug('自动回复已关闭，跳过消息处理');
                return;
            }

            // 检查是否正在处理消息
            if (isProcessingMessage) {
                debug('消息处理中，跳过新的消息');
                return;
            }

            isProcessingMessage = true;
            lastMessageProcessingStartTime = Date.now(); // 记录消息处理开始时间
            
            if (!message?.element) {
                debug('消息无效，跳过处理');
                isProcessingMessage = false;
                return;
            }

            const state = getConversationState(conversationId);
            if (!state) {
                debug('找不到会话状态:', conversationId);
                isProcessingMessage = false;
                return;
            }
            
            // 检查是否是售后申请卡片，如果是则更新消息类型
            if (message.element.querySelector('.apply-card') && 
                (message.element.textContent.includes('消费者申请售后') || 
                 message.element.textContent.includes('退款') || 
                 message.element.textContent.includes('退货'))) {
                debug('[消息类型] 检测到售后申请卡片，更新消息类型为REFUND');
                message.type = MESSAGE_TYPES.REFUND;
            }
            
            // 检查是否是commonCardTemp类型的售后卡片
            if (message.element.querySelector('.commonCardTemp') && 
                (message.element.querySelector('.commonCardTemp .title')?.textContent.includes('售后') ||
                 message.element.querySelector('.commonCardTemp .text-content')?.textContent.includes('售后'))) {
                debug('[消息类型] 检测到平台售后通知消息，更新消息类型为REFUND');
                message.type = MESSAGE_TYPES.REFUND;
            }

            // 检查是否需要转人工
            let shouldTransfer = false;
            if (TransferManager && typeof TransferManager.shouldTransfer === 'function') {
                shouldTransfer = await TransferManager.shouldTransfer(message, state);
            } else {
                // 兼容旧版代码
                if (typeof window.checkShouldTransfer === 'function') {
                    shouldTransfer = await window.checkShouldTransfer(message, state);
                }
            }
            
            // 检查是否包含转人工关键词，即使总开关关闭也需要检查
            const isTransferKeyword = message.content && 
                (message.content.includes('转人工') || 
                 message.content.includes('转接') || 
                 message.content.includes('转售前') || 
                 message.content.includes('转售后'));
                 
            // 如果是转人工关键词，标记为需要处理转人工
            let isHandledByTransfer = false;
                 
            if (shouldTransfer) {
                debug('触发转人工条件，准备转人工');
                
                let transferSuccess = false;
                
                // 使用转接管理器处理转人工
                if (TransferManager && typeof TransferManager.handleTransfer === 'function') {
                    transferSuccess = await TransferManager.handleTransfer(conversationId);
                } else {
                    // 兼容旧版代码
                    if (typeof window.handleTransfer === 'function') {
                        transferSuccess = await window.handleTransfer(conversationId);
                    }
                }
                
                if (transferSuccess) {
                    // 发送转人工成功提示消息
                    await sendMessage('收到，请您稍候一下···');
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    isHandledByTransfer = true;
                    isProcessingMessage = false;
                    return;
                } else {
                    // 转人工失败，检查是否需要自动打标
                    const settings = await getStorageValue('transferSettings', {});
                    if (settings.transferFailMarkStar) {
                        debug('[转人工] 转人工失败，执行自动打标');
                        if (typeof window.actionStarFlag === 'function') {
                            window.actionStarFlag();
                        }
                    }
                    //转失败，不继续处理
                    isHandledByTransfer = true;
                    isProcessingMessage = false;
                    return;
                }
            } else if (isTransferKeyword) {
                // 即使总开关关闭，也检查是否包含转人工关键词
                debug('[转人工] 检测到转人工关键词，但总开关已关闭');
                    
                // 标记所有未回复的消息为已回复
                if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                    debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                    for (const msgElement of message.unrepliedMessages) {
                        await markMessageAsReplied(msgElement, conversationId);
                    }
                } else {
                    // 如果没有未回复消息列表，标记当前消息为已回复
                    await markMessageAsReplied(message.element, conversationId);
                }
                    
                // 转人工关键词消息已处理，不再发送到 Dify
                isHandledByTransfer = true;
                isProcessingMessage = false;
                return;
            }
            
            // 如果已经被转人工逻辑处理过，不再继续处理
            if (isHandledByTransfer) {
                debug('消息已被转人工逻辑处理，不再继续处理');
                isProcessingMessage = false;
                return;
            }
            
            // 检查用户名
            let top_username = getTopUsername();
            if(top_username === 'youke') {
                debug('【游客】跳过游客消息类型回复');
                isProcessingMessage = false;
                return;
            }

            // 根据消息类型处理
            debug("[AI设置] 消息类型", message.type);
            
            // 使用回复处理器处理消息
            if (ReplyProcessor && typeof ReplyProcessor.processReply === 'function') {
                await ReplyProcessor.processReply(message, state, conversationId);
            } else {
                // 兼容旧版代码，使用difyReplyProcess
                if (typeof window.difyReplyProcess === 'function') {
                    await window.difyReplyProcess(message, state, conversationId);
                }
            }
            
            // 处理完成，重置状态
            isProcessingMessage = false;
            
            // 触发消息处理完成事件
            if (EventBus) {
                EventBus.emit('message:processed', { message, conversationId });
            }
            
        } catch (error) {
            debug('处理消息时出错:', error);
            isProcessingMessage = false;
            
            // 触发错误事件
            if (EventBus) {
                EventBus.emit('message:error', { message, conversationId, error });
            }
        }
    }
    
    /**
     * 标记消息为已回复
     * @param {HTMLElement} messageElement - 消息DOM元素
     * @param {string} conversationId - 会话ID
     */
    async function markMessageAsReplied(messageElement, conversationId) {
        try {
            if (!messageElement) {
                debug('标记已回复: 消息元素为空');
                return;
            }
            
            // 生成消息ID
            const messageId = messageElement.id || MessageParser.generateMessageId(messageElement);
            
            // 保存已回复状态
            await saveRepliedMessage(conversationId, messageId);
            
            // 添加可视化标记
            await addVisualReplyMark(messageElement);
            
            debug(`标记消息已回复: ${messageId}`);
        } catch (error) {
            debug('标记消息已回复时出错:', error);
        }
    }
    
    /**
     * 添加可视化回复标记
     * @param {HTMLElement} messageElement - 消息DOM元素
     */
    async function addVisualReplyMark(messageElement) {
        try {
            if (!messageElement) return;
            
            // 检查是否已经有标记
            if (messageElement.querySelector('.ai-reply-mark')) {
                return;
            }
            
            // 创建标记元素
            const markElement = document.createElement('div');
            markElement.className = 'ai-reply-mark';
            markElement.textContent = '已回复';
            markElement.style.cssText = `
                position: absolute;
                top: 0;
                right: 0;
                background-color: rgba(76, 175, 80, 0.7);
                color: white;
                padding: 2px 5px;
                border-radius: 3px;
                font-size: 12px;
                z-index: 10;
            `;
            
            // 添加到消息元素
            messageElement.style.position = 'relative';
            messageElement.appendChild(markElement);
        } catch (error) {
            debug('添加可视化回复标记时出错:', error);
        }
    }
    
    /**
     * 保存已回复消息记录
     * @param {string} conversationId - 会话ID
     * @param {string} messageId - 消息ID
     */
    async function saveRepliedMessage(conversationId, messageId) {
        try {
            // 获取当前存储的已回复消息
            const storageKey = `replied_messages_${conversationId}`;
            const repliedMessages = await getStorageValue(storageKey, {});
            
            // 添加新的消息ID和时间戳
            repliedMessages[messageId] = Date.now();
            
            // 保存回到存储
            await setStorageValue(storageKey, repliedMessages);
            
            debug(`已保存回复状态: ${conversationId} - ${messageId}`);
        } catch (error) {
            debug('保存已回复消息时出错:', error);
        }
    }
    
    /**
     * 检查消息是否已回复
     * @param {string} conversationId - 会话ID
     * @param {string} messageId - 消息ID
     * @returns {boolean} - 是否已回复
     */
    async function isMessageReplied(conversationId, messageId) {
        try {
            const storageKey = `replied_messages_${conversationId}`;
            const repliedMessages = await getStorageValue(storageKey, {});
            return !!repliedMessages[messageId];
        } catch (error) {
            debug('检查消息是否已回复时出错:', error);
            return false;
        }
    }
    
    /**
     * 获取存储值
     * @param {string} key - 存储键
     * @param {*} defaultValue - 默认值
     * @returns {Promise<*>} - 存储值
     */
    async function getStorageValue(key, defaultValue) {
        return new Promise((resolve) => {
            chrome.storage.local.get(key, (result) => {
                resolve(result[key] !== undefined ? result[key] : defaultValue);
            });
        });
    }
    
    /**
     * 设置存储值
     * @param {string} key - 存储键
     * @param {*} value - 存储值
     * @returns {Promise<void>}
     */
    async function setStorageValue(key, value) {
        return new Promise((resolve) => {
            chrome.storage.local.set({ [key]: value }, resolve);
        });
    }
    
    /**
     * 发送消息
     * @param {string} text - 消息文本
     * @returns {Promise<boolean>} - 是否发送成功
     */
    async function sendMessage(text) {
        try {
            // 兼容旧版代码
            if (typeof window.sendToPddCustomer === 'function') {
                return await window.sendToPddCustomer(text);
            }
            
            // 获取输入框和发送按钮
            const inputBox = document.querySelector('.chat-message-input');
            const sendButton = document.querySelector('.chat-message-send');
            
            if (!inputBox || !sendButton) {
                debug('找不到输入框或发送按钮');
                return false;
            }
            
            // 设置输入框的值
            inputBox.value = text;
            
            // 触发输入事件
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
            
            // 点击发送按钮
            sendButton.click();
            
            return true;
        } catch (error) {
            debug('发送消息时出错:', error);
            return false;
        }
    }
    
    /**
     * 获取顶部用户名
     * @returns {string} - 用户名
     */
    function getTopUsername() {
        try {
            const usernameElement = document.querySelector('.chat-user-name');
            return usernameElement ? usernameElement.textContent.trim() : 'unknown';
        } catch (error) {
            debug('获取顶部用户名时出错:', error);
            return 'unknown';
        }
    }
    
    // 公开API
    return {
        ConversationState,
        initConversationState,
        getConversationState,
        processMessage,
        markMessageAsReplied,
        addVisualReplyMark,
        saveRepliedMessage,
        isMessageReplied,
        sendMessage,
        getTopUsername
    };
})();

// 兼容旧版代码
if (typeof window !== 'undefined') {
    // 将关键函数暴露到全局作用域，以便与现有代码兼容
    window.processMessage = window.AIPDD.MessageHandler.processMessage;
    window.markMessageAsReplied = window.AIPDD.MessageHandler.markMessageAsReplied;
    window.addVisualReplyMark = window.AIPDD.MessageHandler.addVisualReplyMark;
    window.isMessageReplied = window.AIPDD.MessageHandler.isMessageReplied;
    window.getTopUsername = window.AIPDD.MessageHandler.getTopUsername;
}