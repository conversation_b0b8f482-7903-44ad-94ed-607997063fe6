// transfer-loader.js - 转人工模块加载器
(function() {
    // 命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Transfer = window.AIPDD.Transfer || {};
    
    // 模块状态
    const moduleStatus = {
        settings: false,
        rules: false,
        manager: false,
        ui: false
    };
    
    /**
     * 加载模块
     * @param {string} modulePath - 模块路径
     * @returns {Promise<void>}
     */
    async function loadModule(modulePath) {
        return new Promise((resolve, reject) => {
            try {
                const script = document.createElement('script');
                script.src = chrome.runtime.getURL(modulePath);
                script.onload = () => resolve();
                script.onerror = (error) => reject(new Error(`加载模块失败: ${modulePath}`));
                document.head.appendChild(script);
            } catch (error) {
                reject(error);
            }
        });
    }
    
    // 转人工模块加载器
    const TransferLoader = {
        /**
         * 初始化转人工功能
         * @returns {Promise<boolean>} 是否成功
         */
        init: async function() {
            console.log('[TransferLoader] 开始初始化转人工模块');
            
            try {
                // 1. 加载Settings模块
                if (!moduleStatus.settings) {
                    await loadModule('src/core/settings/settings-manager.js');
                    moduleStatus.settings = true;
                    console.log('[TransferLoader] Settings模块加载完成');
                }
                
                // 2. 加载Rules模块
                if (!moduleStatus.rules) {
                    await loadModule('src/core/transfer/transfer-rules.js');
                    moduleStatus.rules = true;
                    console.log('[TransferLoader] Rules模块加载完成');
                }
                
                // 3. 加载Manager模块
                if (!moduleStatus.manager) {
                    await loadModule('src/core/transfer/transfer-manager.js');
                    moduleStatus.manager = true;
                    console.log('[TransferLoader] Manager模块加载完成');
                }
                
                // 4. 加载UI模块（可选）
                if (!moduleStatus.ui && window.AIPDD.UI) {
                    try {
                        await loadModule('src/core/ui/settings-dialog.js');
                        moduleStatus.ui = true;
                        console.log('[TransferLoader] UI模块加载完成');
                    } catch (error) {
                        console.warn('[TransferLoader] UI模块加载失败，跳过:', error.message);
                        moduleStatus.ui = true; // 标记为已处理，避免重复尝试
                    }
                }
                
                // 检查是否加载成功
                if (window.TransferManager && 
                    window.AIPDD.Transfer.Manager && 
                    window.AIPDD.Transfer.Rules) {
                    console.log('[TransferLoader] 转人工模块加载成功');
                    
                    // 初始化转人工功能
                    if (window.TransferManager.initialize) {
                        await window.TransferManager.initialize();
                    }
                    
                    return true;
                } else {
                    console.error('[TransferLoader] 转人工模块加载失败');
                    return false;
                }
            } catch (error) {
                console.error('[TransferLoader] 初始化转人工模块出错:', error);
                return false;
            }
        },
        
        /**
         * 加载并初始化转人工模块
         * @returns {Promise<boolean>} 是否成功
         */
        initTransferModules: async function() {
            try {
                // 如果已经加载，则直接返回
                if (this.isLoaded()) {
                    console.log('[TransferLoader] 转人工模块已加载');
                    return true;
                }
                
                // 否则加载并初始化
                return await this.init();
            } catch (error) {
                console.error('[TransferLoader] 初始化转人工模块出错:', error);
                return false;
            }
        },
        
        /**
         * 检查转人工模块是否已加载
         * @returns {boolean} 是否已加载
         */
        isLoaded: function() {
            return window.TransferManager && 
                   window.AIPDD.Transfer && 
                   window.AIPDD.Transfer.Manager && 
                   window.AIPDD.Transfer.Rules;
        }
    };
    
    // 暴露到命名空间
    window.AIPDD.Transfer.Loader = TransferLoader;
    
    // 自动初始化
    if (document.readyState === 'complete') {
        TransferLoader.init();
    } else {
        window.addEventListener('load', () => {
            TransferLoader.init();
        });
    }
})(); 