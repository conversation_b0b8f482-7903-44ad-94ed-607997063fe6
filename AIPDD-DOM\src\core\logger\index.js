/**
 * 日志模块入口文件
 * 统一导出日志相关功能
 */

// 确保依赖模块已加载
(function() {
    'use strict';
    
    // 加载日志管理器
    if (!window.AIPDD || !window.AIPDD.Core || !window.AIPDD.Core.Logger) {
        console.error('[Logger] 日志管理器未加载');
    }
    
    // 创建命名空间
    window.AIPDD = window.AIPDD || {};
    window.AIPDD.Logger = {};
    
    // 获取日志管理器实例
    const LoggerManager = window.AIPDD.Core.Logger.LoggerManager;
    const LogLevels = window.AIPDD.Core.Logger.LogLevels;
    
    // 便捷方法
    const LoggerAPI = {
        // 日志级别常量
        Levels: LogLevels,
        
        // 基本日志方法
        trace: function(message, module = 'general', error = null) {
            LoggerManager.trace(message, module, error);
        },
        
        debug: function(message, module = 'general', error = null) {
            LoggerManager.debug(message, module, error);
        },
        
        info: function(message, module = 'general', error = null) {
            LoggerManager.info(message, module, error);
        },
        
        warn: function(message, module = 'general', error = null) {
            LoggerManager.warn(message, module, error);
        },
        
        error: function(message, module = 'general', error = null) {
            LoggerManager.error(message, module, error);
        },
        
        fatal: function(message, module = 'general', error = null) {
            LoggerManager.fatal(message, module, error);
        },
        
        // 配置方法
        configure: function(options) {
            return LoggerManager.configure(options);
        },
        
        // 日志查询与导出
        getEntries: function(options) {
            return LoggerManager.getLogEntries(options);
        },
        
        clear: function(persistedToo = false) {
            LoggerManager.clearLogs(persistedToo);
        },
        
        export: function(format = 'json') {
            return LoggerManager.exportLogs(format);
        },
        
        // 设置管理
        getSettings: async function() {
            const settings = await window.AIPDD.Settings.get('loggerSettings');
            return settings;
        },
        
        saveSettings: async function(settings) {
            return await LoggerManager.saveSettings(settings);
        },
        
        // 兼容旧版Debug API
        log: function(message, module = 'general', level = 'info') {
            switch (level.toLowerCase()) {
                case 'trace': LoggerManager.trace(message, module); break;
                case 'debug': LoggerManager.debug(message, module); break;
                case 'info': LoggerManager.info(message, module); break;
                case 'warn': LoggerManager.warn(message, module); break;
                case 'error': LoggerManager.error(message, module); break;
                case 'fatal': LoggerManager.fatal(message, module); break;
                default: LoggerManager.info(message, module);
            }
        },
        
        // 创建模块专用日志记录器
        createLogger: function(moduleName) {
            return {
                trace: (message, error = null) => LoggerManager.trace(message, moduleName, error),
                debug: (message, error = null) => LoggerManager.debug(message, moduleName, error),
                info: (message, error = null) => LoggerManager.info(message, moduleName, error),
                warn: (message, error = null) => LoggerManager.warn(message, moduleName, error),
                error: (message, error = null) => LoggerManager.error(message, moduleName, error),
                fatal: (message, error = null) => LoggerManager.fatal(message, moduleName, error)
            };
        },
        
        // 初始化方法
        init: async function() {
            console.log('[Logger] 日志模块初始化');
            await LoggerManager.init();
            
            // 创建全局兼容别名
            if (window.AIPDD.Core && window.AIPDD.Core.Utils) {
                // 保持与旧版Debug API的兼容性
                const oldDebug = window.AIPDD.Core.Utils.Debug;
                
                // 如果旧版Debug存在，保留它的引用
                if (oldDebug) {
                    window.AIPDD.Core.Utils._OldDebug = oldDebug;
                }
                
                // 创建新的Debug对象，使用Logger实现
                window.AIPDD.Core.Utils.Debug = {
                    log: this.log,
                    debug: this.debug,
                    info: this.info,
                    warn: this.warn,
                    error: this.error,
                    
                    // 保留旧版的LogLevels
                    LogLevels: {
                        DEBUG: LogLevels.DEBUG,
                        INFO: LogLevels.INFO,
                        WARN: LogLevels.WARN,
                        ERROR: LogLevels.ERROR,
                        NONE: LogLevels.NONE
                    },
                    
                    // 兼容旧版配置方法
                    configure: function(options) {
                        const newOptions = {};
                        
                        if (options.level !== undefined) {
                            newOptions.level = options.level;
                        }
                        
                        if (options.enabled !== undefined) {
                            newOptions.enabled = options.enabled;
                        }
                        
                        return LoggerManager.configure(newOptions);
                    }
                };
            }
            
            // 记录初始化日志
            this.info('日志模块初始化完成', 'logger');
            
            return this;
        }
    };
    
    // 导出到全局命名空间
    window.AIPDD.Logger = LoggerAPI;
    
    console.log('[Logger] 日志模块加载完成');
})(); 