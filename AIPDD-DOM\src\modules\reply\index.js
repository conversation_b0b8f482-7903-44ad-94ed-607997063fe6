/**
 * 自动回复模块入口文件
 */
(function(global) {
    'use strict';

    // 导入依赖
    const { debug } = global.AIPDD.Core.Utils.Debug;
    const { EventBus } = global.AIPDD.Core.Utils.EventBus;

    // 确保模块命名空间存在
    if (!global.AIPDD) global.AIPDD = {};
    if (!global.AIPDD.Modules) global.AIPDD.Modules = {};
    if (!global.AIPDD.Modules.Reply) global.AIPDD.Modules.Reply = {};

    // 初始化模块
    function init() {
        debug('[模块] 初始化自动回复模块');
        
        // 加载子模块
        try {
            // 动态加载回复处理器
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('src/modules/reply/reply-processor.js');
            script.onload = () => {
                debug('[模块] 回复处理器加载完成');
                
                // 导出兼容接口
                exportLegacyInterfaces();
                
                // 触发模块初始化完成事件
                EventBus.getInstance().emit('module:initialized', 'reply');
            };
            document.head.appendChild(script);
        } catch (error) {
            debug('[模块] 自动回复模块初始化失败:', error);
        }
    }

    // 导出兼容旧代码的接口
    function exportLegacyInterfaces() {
        const replyProcessor = global.AIPDD.Modules.Reply.ReplyProcessor;
        
        // 导出difyReplyProcess函数到全局，以便旧代码调用
        global.difyReplyProcess = (r, s, m) => {
            return replyProcessor.processReply(r, s, m);
        };
        
        // 导出其他必要的回复相关函数
        global.sendDifyTxtRequest = (content, userInfo, conversationId, state, message) => {
            return replyProcessor.sendDifyTxtRequest(content, userInfo, conversationId, state, message);
        };
        
        global.sendDifyProductRequest = (content, userInfo, conversationId, state, message) => {
            return replyProcessor.sendDifyProductRequest(content, userInfo, conversationId, state, message);
        };
        
        global.sendDifyMediaRequest = (emoji_content, userInfo) => {
            return replyProcessor.sendDifyMediaRequest(emoji_content, userInfo);
        };
        
        global.sendDifyImageRequest = (imageUrl, userInfo) => {
            return replyProcessor.sendDifyImageRequest(imageUrl, userInfo);
        };
    }

    // 模块API
    const ReplyModule = {
        init,
        
        // 获取回复处理器实例
        getProcessor() {
            return global.AIPDD.Modules.Reply.ReplyProcessor;
        }
    };

    // 导出模块API
    global.AIPDD.Modules.Reply.API = ReplyModule;

})(window);